import {StyleSheet} from "react-native";
import { COLORS,FONT,NUMBERS,TENS } from "../../constants";

const homeStyles = StyleSheet.create({
    container: {
      flex: NUMBERS.one,
      borderWidth:NUMBERS.zero
    },
    videoContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    wrapper:
    {
      flex:NUMBERS.one,
      borderColor: COLORS.borderPrimaryColor,
      borderWidth: NUMBERS.four,
      borderBottomWidth:NUMBERS.zero
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    button: {
      padding: NUMBERS.six,
      alignItems: 'center',
      justifyContent: 'center',
      flex: NUMBERS.one,
      shadowColor: '#000',
      shadowOffset: { width: NUMBERS.zero, height: NUMBERS.two },
      shadowOpacity: 0.3,
      shadowRadius: NUMBERS.two,
      elevation: NUMBERS.three, 
    },
    buttonText: {
      color: 'white',
      fontFamily:FONT.bold
      
    },
    buttonContent:
    {
      flexDirection: 'row', // Display content in a row
      alignItems: 'center', // Align content vertically in the center
      justifyContent: 'space-between', // Space items evenly in the row
     
      
     
    },
    buttonIcon:
    {
      marginRight:5,
      marginLeft:5,
     marginBottom:5
    },
    glossyButton: {
      borderWidth: NUMBERS.one,
      borderColor: 'rgba(255, 255, 255, 0.5)',
      borderBottomWidth: NUMBERS.zero,
      shadowColor: 'rgba(0, 0, 0, 0.4)',
      shadowOffset: { width: NUMBERS.zero, height: NUMBERS.four },
      shadowOpacity: 0.8,
      shadowRadius: NUMBERS.five,
      elevation: NUMBERS.two,
    },
    safeArea: {
      flex: NUMBERS.one,
      backgroundColor: '#000',
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: COLORS.borderPrimaryColor,
      borderWidth: NUMBERS.four,
      borderBottomWidth:NUMBERS.zero
     
    },
    overlay: {
      position: 'absolute',
      top: NUMBERS.zero,
      left: NUMBERS.zero,
      right: NUMBERS.zero,
      height: '70%', // Adjust this value as needed for the size of the white overlay
      backgroundColor: 'rgba(255, 255, 255, 0.NUMBERS.one)',
    },
  });
  export default homeStyles;