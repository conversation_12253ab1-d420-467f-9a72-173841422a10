import {StyleSheet} from "react-native";
import { COLORS,FONT,NUMBERS } from "../../constants";


export const styles = StyleSheet.create({
    socialMediaWrapper: {
      flexDirection:"row",
      backgroundColor: COLORS.homedisabledPrimaryBtnColor,
      padding:5,
      borderRightColor: COLORS.borderPrimaryColor,
      borderRightWidth: NUMBERS.four,
      borderLeftColor: COLORS.borderPrimaryColor,
      borderLeftWidth: NUMBERS.four,
    },
    content: {
      flex: 1,
      // Your main content styles here
    },
    socialMediaContainer: {
      backgroundColor: "transparent",
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      direction: 'ltr'
    },
    absoluteContainer: {
      position: 'absolute',
      flexDirection: 'row',
      flex:0,
      left:0,
      top: 0,
      bottom: 0,
      alignItems: 'center',
      justifyContent: 'center',
      width: "33.5%", // Fixed width
      
    },
    iconContainer: {
      marginHorizontal: 4,
      paddingVertical: 5,
      padding: 5,
      borderWidth: 1,
      borderColor: COLORS.textColor,
      borderRadius: 5,
      backgroundColor: COLORS.textColor,
      height:32,
      width:32,
      alignItems:"center"
     
    },
    touchableContainer: {
      backgroundColor: 'lightgray',
      paddingVertical: 0,
      height:70,
      width: '100%',
      
     
    },
    buttonText: {
        color: COLORS.textColor,
        fontFamily:FONT.medium
      },
      buttonContent: {
       
        alignItems: 'center',
        justifyContent: 'center',
      },
      dishIcon: {
        marginTop: 3,
      },
  });