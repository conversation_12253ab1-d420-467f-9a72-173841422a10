import axios from "axios";
import { encode as btoa } from 'base-64';
import FormData from "form-data";

const EmailSender = async (to, subject, text) => {
  const apiUrl = "https://api.mailgun.net/v3/sandboxda5dff3224ea4f568fc9cd14d67bab57.mailgun.org/messages";
  const apiKey = "**************************************************";
  const from = "VTOTT ADMIN <<EMAIL>>";

  // Encode the API key using the base-64 library
  const encodedApiKey = btoa(`api:${apiKey}`);
  const authHeader = `Basic ${encodedApiKey}`;

  const formData = new FormData();
  formData.append("from", from);
  to.forEach((email) => formData.append("to", email));
  formData.append("subject", subject);
  formData.append("text", text);

  try {
    const response = await axios.post(apiUrl, formData, {
      headers: {
        "Authorization": authHeader,
      
      },
    });

    console.log("Email sent successfully:", response.data);
  } catch (error) {
    console.error("Error sending email:", error);
  }
};

// Export EmailSender when you want to send the email
export { EmailSender };
