import axios from "axios";
import AsyncStorage from '@react-native-async-storage/async-storage';






export async function getAccessToken(apiKey) {
  try {
    // Check if a token exists in AsyncStorage
    const storedToken = await AsyncStorage.getItem('access_token');
    const storedTokenTimestamp = await AsyncStorage.getItem('access_token_timestamp');

    if (storedToken && storedTokenTimestamp) {
      const currentTime = Date.now();
      const tokenExpirationTime = parseInt(storedTokenTimestamp) + 15 * 60 * 60 * 1000; // 15 hours in milliseconds

      if (currentTime < tokenExpirationTime) {
        // The stored token is valid, return it
        return storedToken;
      }
    }

    // Set up the request payload as FormData
    const formData = new FormData();
    formData.append('grant_type', 'client_credentials');

    // Set up the request headers
    const headers = new Headers({
      Authorization: `Basic ${apiKey}`, // Corrected "basic" to "Basic"
    });

    // Make the POST request to obtain the access token
    const response = await fetch('https://alrayyan.tv/oauth/token/', {
      method: 'POST',
      headers: headers,
      body: formData,
    });

    // Check if the request was successful
    if (!response.ok) {
      throw new Error('Error obtaining access token');
    }

    // Parse the response JSON to get the access token
    const data = await response.json();

    // Store the new access token in AsyncStorage with a timestamp
    await AsyncStorage.setItem('access_token', data.access_token);
    await AsyncStorage.setItem('access_token_timestamp', Date.now().toString());

    return data.access_token;
  } catch (error) {
    console.error('Error obtaining/accessing the access token:', error);
    throw error;
  }
}



export async function getContest(token)
{
  
  const result = await axios.post(
    "https://alrayyan.tv/wp-json/tvapi/v2/contests?contest_type=active",
    {},
    {
      headers: {
        Authorization:`Bearer ${token}`, // Use the access token as Bearer token
      },
    },
    
  )
  .then((response) => {
    return response.data
  })
  .catch((error) => {
    throw new Error(error);
    
  
  });
  return result;

}