export const extractImagesLinksTextFromVCMarkup=(markup)=> {
    const result = [];
  
    // Regular expressions to match flat_box, links, and text elements
    const flatBoxRegex = /\[flat_box\s+image="([^"]+)"\s+icon="([^"]+)"\s*\]/g;
    const linkRegex = /<a\s+href="([^"]+)">(.*?)<\/a>/g;
    const textRegex = /<p[^>]*>(.*?)<\/p>/g;
  
    // Extract flat_box elements (images and icons)
    let flatBoxMatch;
    while ((flatBoxMatch = flatBoxRegex.exec(markup)) !== null) {
      const imageUrl = flatBoxMatch[1];
      const iconText = flatBoxMatch[2];
      result.push({ type: 'image', imageUrl, text: iconText });
    }
  
    // Extract links and text elements
    const combinedText = markup.replace(flatBoxRegex, ''); // Remove flat_box elements for text extraction
    let linkMatch;
    while ((linkMatch = linkRegex.exec(combinedText)) !== null) {
      const linkUrl = linkMatch[1];
      const linkText = linkMatch[2];
      result.push({ type: 'link', linkUrl, text: linkText });
    }
    let textMatch;
    while ((textMatch = textRegex.exec(combinedText)) !== null) {
      const text = textMatch[1];
      result.push({ type: 'text', text });
    }
  
    return result;
  }

export function extractHeaderAndStarredWords(text) {
    const lines = text.split('*').map(line => line.trim()); // Split by asterisk (*) and trim each line
    const header = lines[0].replace(/:$/, ''); // First line is the header (remove trailing colon)
    const starredWords = lines.slice(1); // The rest of the lines are starred words
  
    return { header, starredWords };
  }

export function extractContent(inputHtml) {
      // Regular expression to match Arabic text within <p> tags with style="direction: rtl;"
      const regex = /<p(?:.*?)?>(.*?)<\/p>/g;
  const matches = [];
  let match;

  while ((match = regex.exec(inputHtml)) !== null) {
    const innerText = match[1];

    // Check if the <p> tag contains Arabic characters
    if (isArabicText(innerText)) {
      const cleanedText = innerText.replace(/<[^>]*>/g, ''); // Remove HTML tags
      matches.push(cleanedText);
    }
  }

  return matches;
  }

function isArabicText(text) {
    // Use a regular expression to check if the text contains Arabic characters
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicRegex.test(text);
  }
 
  

  export function extractContentBetweenTags(html) {
    // Create a temporary div element to parse the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Find all <p> elements with style="text-align: right;"
    const paragraphs = tempDiv.querySelectorAll('p[style="text-align: right;"]');

    // Initialize an array to store the extracted content
    const extractedContent = [];

    // Iterate through the found paragraphs and extract their inner text
    paragraphs.forEach(paragraph => {
        extractedContent.push(paragraph.innerText);
    });
    console.log(extractedContent);
    return extractedContent;
}