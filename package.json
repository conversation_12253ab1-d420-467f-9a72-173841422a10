{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "postinstall": "patch-package"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/slider": "4.4.2", "@react-native-firebase/app": "^18.7.2", "@react-native-firebase/crashlytics": "^18.7.2", "@react-native-firebase/messaging": "^18.7.2", "@react-native-firebase/perf": "^18.7.2", "@react-navigation/drawer": "^6.6.4", "@sentry/react-native": "5.10.0", "axios": "^1.5.0", "base-64": "^1.0.0", "expo": "^49.0.3", "expo-application": "~5.3.0", "expo-av": "~13.4.1", "expo-build-properties": "~0.8.3", "expo-constants": "~14.4.2", "expo-dev-client": "~2.4.13", "expo-device": "~5.4.0", "expo-document-picker": "~11.5.4", "expo-font": "~11.4.0", "expo-image": "~1.3.5", "expo-keep-awake": "~12.3.0", "expo-linear-gradient": "~12.3.0", "expo-linking": "~5.0.2", "expo-localization": "~14.3.0", "expo-mail-composer": "~12.3.0", "expo-router": "2.0.0", "expo-screen-orientation": "~6.0.6", "expo-splash-screen": "~0.20.4", "expo-status-bar": "~1.6.0", "form-data": "^4.0.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.10", "react-native-gesture-handler": "~2.12.0", "react-native-image-zoom-viewer": "^3.0.1", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "^6.8.1", "react-native-paper": "^5.10.6", "react-native-reanimated": "~3.3.0", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.27.0", "react-native-tab-view": "^3.5.2", "react-native-web": "~0.19.6", "react-native-webview": "13.2.2", "react-native-youtube": "^2.0.2", "sentry-expo": "~7.1.0"}, "devDependencies": {"@babel/core": "^7.19.3", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0"}, "private": true}