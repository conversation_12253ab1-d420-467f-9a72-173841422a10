import React, { useState, useEffect, useRef } from "react";
import {
  View,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  Animated,
  Modal,
  Dimensions,
  Platform
} from "react-native";
import * as ScreenOrientation from 'expo-screen-orientation';
import { FontAwesome } from "@expo/vector-icons";
import { Video } from "expo-av";
import { COLORS } from "../../constants";
import { router, useFocusEffect } from "expo-router";
import { isLoaded } from "expo-font";
import Slider from "@react-native-community/slider";
import { useHome } from "../../context/home";
import { LogBox } from 'react-native';
import { activateKeepAwakeAsync } from "expo-keep-awake";
LogBox.ignoreLogs([
  /.*new NativeEventEmitter\(\) was called with a non-null argument without the required addListener method.*/,
]);
const VideoPlayer = ({ videoBitRate, width, channel, quality,full,position }) => {
 
  const [bitrate, setBitrate] = useState(quality);
  const videoRef = useRef(null);
  const [showBitrateOptions, setShowBitrateOptions] = useState(false);
  const [source, setSource] = useState(videoBitRate[bitrate]);
  const [isBuffering, setIsBuffering] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(false); // State to show/hide controls
  const bitrateOptions = ["1080p", "720p", "480p", "360p", "auto"];
  const [progress, setProgress] = useState(0);
  const [videoPosition, setVideoPosition] = useState(0); // Current video position
  const [videoDuration, setVideoDuration] = useState(0); 
  const tv = channel?"alrayyan":"oldrayyan";
  const {toggleHome,home} =useHome();

  const hideControlsDelay = 15000; // 15 seconds delay to hide controls

  // Animation for showing/hiding controls
  const controlsOpacity = useRef(new Animated.Value(1)).current;

  const hideControls = () => {
    setShowControls(false);
    setShowBitrateOptions(false);
  };
  const onLoadStarted = ()=>
  {
    setIsBuffering(true);
  }

  const showControlsWithDelay = () => {
    setShowControls(true);

    // Reset the hide delay timer
    clearTimeout(hideControlsTimer);

    // Start the timer to hide controls after 15 seconds
    hideControlsTimer = setTimeout(hideControls, hideControlsDelay);
  };

  useEffect(() => {
    // Load the video with the selected bitrate
    setSource(videoBitRate[bitrate]);
    activateKeepAwakeAsync();
  }, [bitrate, channel]);


  

  const handleBitrateClick = (option) => {
    setBitrate(option);
    setIsBuffering(true);
    setShowBitrateOptions(false);
  };

  const handleFullScreenClick = async() => {
    // Lock the orientation based on the updated value of showModal
   
    // Toggle the showModal state after locking the orientation
    if(Platform.OS==="android")
    {
      toggleHome({position:videoPosition,channel:channel?"alrayyan":"oldrayyan"})
      router.push('/home/'+tv+"?pos="+videoPosition);
    }
    else
    {
      await videoRef.current.presentFullscreenPlayer();
    }

     
    

  };
  

  const handlePlaybackStatusUpdate = (status) => {
    
    setIsPlaying(status.isPlaying);


    if (status.isBuffering && !status.isPlaying) {
      setIsBuffering(true);
    } else {
      setIsBuffering(false);
      setVideoPosition(status.positionMillis);
      setVideoDuration(status.durationMillis);
     
      
    }
    const calculatedProgress = (status.positionMillis / status.durationMillis) || 0;
    setProgress(calculatedProgress);
  };

  const handlePlayPause = ()=>
  {
    if (isPlaying && showControls) {
      videoRef?.current?.pauseAsync();
    } else {
      if(isLoaded)
      videoRef?.current?.playAsync();
    }
  }


  useFocusEffect(
    React.useCallback(() => {
      setIsBuffering(true);
  
      async function videoLoad()
      {
        try {
          await videoRef?.current?.loadAsync({ uri: source });
         
          if(home){ if(home.channel===tv){videoRef?.current?.setPositionAsync(home.position);}} 
          await videoRef?.current?.playAsync();
        } catch (error) {
          console.error('Error loading or playing the video:', error);
        }
      }
      videoLoad();
  
      return () => {
        if (videoRef.current) {
          videoRef?.current?.stopAsync();
          videoRef.current.unloadAsync();
        }
      };
    }, [videoRef, source])
  );
  

  // Timer to hide controls after 15 seconds of inactivity
  let hideControlsTimer;

  return (
    <>
        <View style={{ flex: 1,alignItems:"center",justifyContent:"center"}}>
      {showControls &&   (<TouchableOpacity
        style={{ position: "absolute", top: 60, left: 20, zIndex: 1 }}
        onPress={() => setShowBitrateOptions(!showBitrateOptions)}
      >
        <FontAwesome name="cog" size={24} color="white" />
      </TouchableOpacity>) }
    

      {showBitrateOptions && (
        <View
          style={{
            position: "absolute",
            top: 85,
            left: 20,
            backgroundColor: "rgba(0,0,0,0.7)",
            padding: 10,
            borderRadius: 5,
            zIndex: 2,
          }}
        >
          {bitrateOptions.map((option) => (
            <TouchableOpacity
              key={option}
              onPress={() => handleBitrateClick(option)}
              style={{
                backgroundColor:
                  option === bitrate ? COLORS.primaryHlColor : "transparent",
                alignItems: "center",
              }}
            >
              <Text style={{ color: "white" }}>{option}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

       {
      ( <Video
          ref={videoRef}
          //source={{ uri: source }}
          rate={1.0}
          volume={1.0}
          isMuted={false}
          resizeMode="contain"
          style={{
            width: width,
            height: full?"100%":(9 / 16) * width,
            aspectRatio: 16 / 9,
          }}
          onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
          onLoadStart={onLoadStarted}
          onTouchStart={showControlsWithDelay} // Show controls on touch
          ignoreSilentSwitch={'ignore'}
           playsInSilentModeIOS={true}
        />)
       }

      {isBuffering && (
        <View
          style={{
            position: "absolute",
            alignItems: "center",
            justifyContent: "center",
            width: "100%",
            height: "100%",
          }}
        >
          <ActivityIndicator size="large" color="white" />
        </View>
      )}

      {showControls && !isBuffering && (
        <TouchableOpacity
          style={styles.playButton}
          onPress={() => { handlePlayPause()    }}
        >
          <FontAwesome
            name={isPlaying ? "pause" : "play"}
            size={24}
            color="white"
          />
        </TouchableOpacity>
      )}

      {/* Add the progress bar */}
      {showControls && (
        <Slider
        style={{ width: width, height: 40, position: "absolute", bottom: full?0:(9 / 16)*width-60 }}
          value={videoPosition}
          maximumValue={videoDuration}
          minimumValue={0}
          minimumTrackTintColor={COLORS.primaryHlColor}
          thumbTintColor={COLORS.primaryHlColor}
          maximumTrackTintColor={COLORS.gray2}
          onValueChange={(value) => {
            if (videoRef.current) {
              videoRef.current.setPositionAsync(value);
            }
          }}
         
        />
      )}

      {showControls && !full && (  <TouchableOpacity
        style={{
          position: "absolute",
          bottom: 60,
          left: 20,
          zIndex: 1,
        }}
        onPress={handleFullScreenClick}
      >
        <FontAwesome name="expand" size={24} color="white" />
      </TouchableOpacity>)}

    
    </View>

    </>

  );
};

const styles = StyleSheet.create({
  playButton: {
    position: "absolute",
    alignItems:"center",
    justifyContent:"center",
    
    backgroundColor: "rgba(0,0,0,0.7)",
    width: 46,
    height: 46,
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
  },
  modal: {
    flex: 1,
    width:"100%",
    height:"100%",
    alignItems:"center",
    backgroundColor: COLORS.backgroundColor,
    top:0,
    bottom:0,
    left:0,
    right:0
   
    
  },
   closeButton: {
    position: 'absolute',
    top: 20,
    left: 20,
    zIndex: 1,
  }
});

export default VideoPlayer;
