import { Tabs } from "expo-router";
import { I18nManager, View } from "react-native";
import { COLORS,TENS, TEXTDATA } from "../../../constants";
import { MaterialCommunityIcons,Ionicons } from "@expo/vector-icons";



I18nManager.forceRTL(true);
export default function TabsLayout() {
    return (
      <View style={{ flex: 1,backgroundColor:"rgba(0, 0, 0, 0.5)" }}>
       
        <Tabs
          screenOptions={{
            headerShown: false,
            
            tabBarInactiveTintColor: COLORS.textColor,
            tabBarActiveBackgroundColor: COLORS.primaryHlColor,
            tabBarActiveTintColor: COLORS.textColor,
            tabBarStyle: {
              borderRightColor: COLORS.borderPrimaryColor,
              borderRightWidth: 4,
              borderLeftColor: COLORS.borderPrimaryColor,
              borderLeftWidth: 4,
              backgroundColor: COLORS.menuColor,
              borderTopWidth: 1,
              borderTopColor:COLORS.borderTopColor,
              borderBottomWidth: 0.5,
              borderBottomColor:COLORS.borderTopColor,
              
  
            }
          }}
          
        
        >
          <Tabs.Screen
            name="home"
            options={{
              tabBarLabel: TEXTDATA.menuFirst,
              title:TEXTDATA.menuFirst,
              tabBarItemStyle:
              {
                borderLeftWidth:0,
                paddingBottom:2,
                
              },
              tabBarIcon: () => (
               <Ionicons
               name="albums-outline"
               size={TENS.twenty}
               color={COLORS.textColor}
               ></Ionicons>
              )
            }}
  
          />
          <Tabs.Screen
            name="latest"
            options={{
              tabBarLabel: TEXTDATA.menuSecond,
              
              title: TEXTDATA.menuSecond,
              tabBarItemStyle:
              {
               
                paddingBottom:2,
                borderLeftColor:COLORS.borderTertaryColor,
                borderLeftWidth:1,
                
              },
              tabBarIcon: () => (
                <MaterialCommunityIcons
                  name="television-classic"
                  color={COLORS.textColor}
                  size={TENS.twenty}
                />
              )
            }}
          />
          <Tabs.Screen
            name="shows"
            options={{
              tabBarLabel: TEXTDATA.menuThird,
              title: TEXTDATA.menuThird,
              tabBarStyle:
              {
                backgroundColor:'rgba(0,0,0,0.8)'
              },
              tabBarItemStyle:
              {
               
                paddingBottom:2,
                borderLeftColor:COLORS.borderTertaryColor,
                borderLeftWidth:1,
                
              },
              
              tabBarIcon: () => (
                <MaterialCommunityIcons
                  name="newspaper-variant-outline"
                  color={COLORS.textColor}
                  size={TENS.twenty}
                />
              )
            }}
          />
            <Tabs.Screen
            name="news"
            options={{
              tabBarLabel: TEXTDATA.menuThird,
              title: TEXTDATA.menuThird,
              href:null,
              tabBarStyle:
              {
                backgroundColor:'rgba(0,0,0,0.8)'
              },
              tabBarItemStyle:
              {
               
                paddingBottom:2,
                borderLeftColor:COLORS.borderTertaryColor,
                borderLeftWidth:1,
                
              },
              
              tabBarIcon: () => (
                <MaterialCommunityIcons
                  name="newspaper-variant-outline"
                  color={COLORS.textColor}
                  size={TENS.twenty}
                />
              )
            }}
          />
           <Tabs.Screen
            name="contest"
            options={{
              tabBarLabel: TEXTDATA.menuThird,
              title: TEXTDATA.menuThird,
              href:null,
              tabBarStyle:
              {
                backgroundColor:'rgba(0,0,0,0.8)'
              },
              tabBarItemStyle:
              {
               
                paddingBottom:2,
                borderLeftColor:COLORS.borderTertaryColor,
                borderLeftWidth:1,
                
              },
              
              tabBarIcon: () => (
                <MaterialCommunityIcons
                  name="newspaper-variant-outline"
                  color={COLORS.textColor}
                  size={TENS.twenty}
                />
              )
            }}
          />
           <Tabs.Screen
            name="album"
            options={{
              tabBarLabel: TEXTDATA.menuThird,
              title: TEXTDATA.menuThird,
              href:null,
              tabBarStyle:
              {
                backgroundColor:'rgba(0,0,0,0.8)'
              },
              tabBarItemStyle:
              {
               
                paddingBottom:2,
                borderLeftColor:COLORS.borderTertaryColor,
                borderLeftWidth:1,
                
              },
              
              tabBarIcon: () => (
                <MaterialCommunityIcons
                  name="newspaper-variant-outline"
                  color={COLORS.textColor}
                  size={TENS.twenty}
                />
              )
            }}
          />
           <Tabs.Screen
            name="frequency"
            options={{
              tabBarLabel: TEXTDATA.menuThird,
              title: TEXTDATA.menuThird,
              href:null,
              tabBarStyle:
              {
                backgroundColor:'rgba(0,0,0,0.8)'
              },
              tabBarItemStyle:
              {
               
                paddingBottom:2,
                borderLeftColor:COLORS.borderTertaryColor,
                borderLeftWidth:1,
                
              },
              
              tabBarIcon: () => (
                <MaterialCommunityIcons
                  name="newspaper-variant-outline"
                  color={COLORS.textColor}
                  size={TENS.twenty}
                />
              )
            }}
          />
           <Tabs.Screen
            name="contact"
            options={{
              tabBarLabel: TEXTDATA.menuThird,
              title: TEXTDATA.menuThird,
              href:null,
              tabBarStyle:
              {
                backgroundColor:'rgba(0,0,0,0.8)'
              },
              tabBarItemStyle:
              {
               
                paddingBottom:2,
                borderLeftColor:COLORS.borderTertaryColor,
                borderLeftWidth:1,
                
              },
              
              tabBarIcon: () => (
                <MaterialCommunityIcons
                  name="newspaper-variant-outline"
                  color={COLORS.textColor}
                  size={TENS.twenty}
                />
              )
            }}
          />
        </Tabs>
      </View>
    );
  }