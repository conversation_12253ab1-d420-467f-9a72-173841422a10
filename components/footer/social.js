import React from 'react';
import { View,FlatList,TouchableOpacity, Text,StyleSheet } from 'react-native';
import { <PERSON>ont<PERSON>wes<PERSON>,Feather } from '@expo/vector-icons';
import { COLORS,NUMBERS, TEXTDATA } from '../../constants';
import { styles } from '../../styles/components/social.style';
import homeStyles from '../../styles/pages/home.style';
import { LinearGradient } from 'expo-linear-gradient';
import * as Linking from 'expo-linking';




const SocialMediaList = () => {
    const socialMediaData = [
        { id: '4', name: 'YouTube', icon: 'youtube-play',link:"https://www.youtube.com/user/AlrayyanTV" }, // YouTube play button square icon
        { id: '1', name: 'Facebook', icon: 'facebook',link:"https://www.facebook.com/AlRayyanTV" },
        { id: '2', name: 'Twitter', icon: 'x',link:"https://twitter.com/AlrayyanTV?ref_src=twsrc%5Etfw" },
        { id: '5', name: 'Snapchat', icon: 'snapchat',link:"https://www.snapchat.com/add/alrayyan.tv" }, // Snapchat square icon
        { id: '3', name: 'Instagram', icon: 'instagram',link:"https://www.instagram.com/alrayyantv/?hl=en" },
        // Add more social media platforms as needed
      ];

      const disableBtnColor =[COLORS.homedisabledFourthBtnColor,COLORS.homedisabledSecondBtnColor,COLORS.homedisabledTertaryBtnColor,COLORS.homedisableColor,COLORS.homedisabledPrimaryBtnColor,];
      const activeBtnColor = [COLORS.homeTertaryBtnColor,COLORS.homeSecondBtnColor,COLORS.homeTertaryBtnColor,COLORS.primaryHlColor,COLORS.homePrimaryBtnColor,];

  const renderItem = ({ item }) => {
    return (
      <TouchableOpacity style={styles.iconContainer} onPress={()=>{Linking.openURL(item.link)}}>
       {
        item.name==="Twitter"?<Feather name={item.icon} size={20} color={COLORS.gray} style={{backgroundColor:"transparent"}}></Feather>
        : <FontAwesome name={item.icon} size={20} color={COLORS.gray} style={{backgroundColor:"transparent"}} />
       }
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.socialMediaWrapper}>
 <LinearGradient
              colors={disableBtnColor}

              locations={[0.15,0.45, 0.5,0.75, 1]}
              start={{ x: NUMBERS.zero, y: NUMBERS.zero }}
              end={{ x: NUMBERS.zero, y: NUMBERS.one }}
              style={{ ...StyleSheet.absoluteFillObject }}
            />
            <View style={homeStyles.overlay}></View>

    <View style={styles.socialMediaContainer}>
    {/* SocialMediaList */}
   
    <FlatList
      data={socialMediaData}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      horizontal
    />
   
  </View>
  {/* <View style={styles.absoluteContainer}>
<TouchableOpacity style={styles.touchableContainer}>
     
      <LinearGradient
              colors={activeBtnColor}

              locations={[0.15,0.45, 0.5,0.75, 1]}
              start={{ x: NUMBERS.zero, y: NUMBERS.zero }}
              end={{ x: NUMBERS.zero, y: NUMBERS.one }}
              style={{ ...StyleSheet.absoluteFillObject }}
            />
            <View style={homeStyles.overlay}></View>
      <View style={styles.buttonContent} >
      
        <MaterialCommunityIcons name="satellite-uplink" size={30} color={COLORS.textColor} style={styles.dishIcon} />
            <Text style={styles.buttonText}>{TEXTDATA.homeFirstBarName}</Text>
      </View>
    </TouchableOpacity>
</View> */}
  </View>
  );
};



export default SocialMediaList;
