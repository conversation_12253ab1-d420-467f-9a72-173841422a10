import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONT } from '../../constants';
import IconButton from '../icons/btnIcon';
import burger from "../../assets/burger.png";
import signal from "../../assets/signal.png";
import search from "../../assets/search.png";
import { useRouter,useRootNavigation,useNavigation } from 'expo-router';
import CustomDrawer from '../custom/drawer';

const SecondHeader = ({ title,isDrawerOpen,openDrawer,closeDrawer  }) => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>

     
      {/* Right Icons (Frequency and Bar) */}
      <View style={styles.rightContent}>
        <IconButton imgStyle={styles.iconImage} source={burger} handlePress={()=>{ openDrawer()}} />
        <IconButton imgStyle={styles.iconImage} source={signal} handlePress={()=>{ navigation.navigate('frequency')}}/>
      </View>
    

      {/* Center Title and Icon */}
      <View style={styles.centerContent}>
        
        <Text style={styles.title}>{title}</Text>
      </View>

        {/* Left Icon (Search) */}
        <IconButton imgStyle={styles.headerImage} source={search} handlePress={()=>{navigation.navigate('search')}} />
        <CustomDrawer visible={isDrawerOpen} onClose={closeDrawer} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60, // Set your custom header height here
    backgroundColor: COLORS.headerColor, // Customize the header background color
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  title: {
    color: COLORS.textColor, // Customize the title color
    fontSize: 20, // Customize the title font size,
    fontFamily:FONT.medium,
    fontWeight:"bold"
  },
  icon: {
    marginRight: 8,
    color: COLORS.textColor,
   
  },
  leftIcon: {
    marginLeft: 10,
    color: COLORS.textColor,
    
  },
  iconButton: {
    height: 30,
    width: 35,
  },
  headerImage: {
    height: 30,
    width: 30,
    marginLeft: 10,
    
  },
  iconImage: {
    height: 30,
    width: 35,
    marginLeft: 10,
    zIndex:10
  },
  centerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    right:21
  },
  rightContent: {
    flexDirection: 'row',
  },
});

export default SecondHeader;
