import React,{useState} from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS,FONT } from '../../constants';
import { MaterialCommunityIcons, FontAwesome } from '@expo/vector-icons';
import IconButton from '../icons/btnIcon';
import burger from "../../assets/burger.png";
import signal from "../../assets/signal.png";
import blank from "../../assets/blank.png";
import { router,useNavigation } from 'expo-router';
import CustomDrawer from '../custom/drawer';


const CustomHeader = ({ title,isDrawerOpen,openDrawer,closeDrawer }) => {

  const navigation = useNavigation();


  
  return (
    <View style={styles.container}>

     
      {/* Right Icons (Frequency and Bar) */}
      <View style={styles.rightContent}>
        <IconButton imgStyle={styles.iconImage} source={burger} handlePress={()=>{openDrawer()}}  />
        <IconButton imgStyle={styles.iconImage} source={signal} handlePress={()=>{navigation.navigate('frequency')}} />
      </View>
    

      {/* Center Title and Icon */}
      <View style={styles.centerContent}>
        <MaterialCommunityIcons name="television-classic" size={15} style={styles.icon} />
        <Text style={styles.title}>{title}</Text>
      </View>

        {/* Left Icon (Search) */}
        <IconButton imgStyle={styles.iconImage} source={blank} />
        <CustomDrawer visible={isDrawerOpen} onClose={closeDrawer} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60, // Set your custom header height here
    backgroundColor: COLORS.headerColor, // Customize the header background color
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    color: COLORS.textColor, // Customize the title color
    fontSize: 15, // Customize the title font size
    fontFamily:FONT.bold
    
  },
  icon: {
    marginRight: 8,
    color: COLORS.textColor,
   
  },
  leftIcon: {
    marginLeft: 10,
    color: COLORS.textColor,
    
  },
  iconButton: {
    height: 30,
    width: 35,
  },
  iconImage: {
    height: 30,
    width: 35,
    marginLeft: 10,
  },
  centerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    right:21
  },
  rightContent: {
    flexDirection: 'row',
  },
});

export default CustomHeader;
