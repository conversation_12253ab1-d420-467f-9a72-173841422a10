import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, FONT } from '../../constants';
import { FontAwesome } from '@expo/vector-icons';
import IconButton from '../icons/btnIcon';
import signal from "../../assets/signal.png";
import blank from "../../assets/blank.png";
import { router,useNavigation } from 'expo-router';


const ThirdHeader = ({ title }) => {
  const goBack = () => {
    router.back();
  }
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* Left Icon (Search) */}
      <View style={styles.rightContent}>
        <TouchableOpacity style={[styles.topDown, {height: 26}]} onPress={goBack}>
          <FontAwesome name="chevron-right" style={styles.leftIcon} size={20}></FontAwesome>
        </TouchableOpacity>
        <IconButton imgStyle={[styles.iconImage,{marginRight: 30}]} source={signal} handlePress={()=>{navigation.navigate('frequency')}} />
      </View>
  

      {/* Center Title and Icon */}
      <View style={styles.centerContent}>
        <Text style={styles.title} numberOfLines={1} ellipsizeMode='tail'>{title}</Text>
      </View>

      {/* Right Icons (Frequency and Bar) */}
   
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60, // Set your custom header height here
    backgroundColor: COLORS.headerColor, // Customize the header background color
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10, // Add padding to the container to ensure text visibility
  },
  title: {
    // Allow the title to expand and take available space
    color: COLORS.textColor, // Customize the title color
    fontSize: 18, // Customize the title font size
    textAlign: 'center',
    paddingRight:15,
    fontFamily:FONT.medium,
    fontWeight:"bold"

   
   
  },
  leftIcon: {
    color: COLORS.textColor,
  },
  iconImage: {
    height: 30,
    width: 35,
    marginLeft: 10,
  },
  centerContent: {
// Allow the title to expand and take available space
    flexDirection: 'row',
    alignItems:'center',
    justifyContent:"center",
    width:"100%",
    flex:1,
    right:25
   
  },
  rightContent: {
    flexDirection: 'row',
  },
  topDown: {
    marginTop: 10,
  },
});

export default ThirdHeader;
