import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { WebView } from 'react-native-webview';
import { ActivityIndicator } from 'react-native';

const iframeUrl = 'https://static.vidgyor.com/player/vod/v6/html/rgb.html?accountId=vprism&profile=rgb&playerDivId=vidgyor&videoUrl=https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/playlist_dvr.m3u8';

const renderLoading = ()=>
{
  return(<ActivityIndicator style = {{alignItems: 'center',justifyContent: 'center'}}/>)
}
const IframeComponent = () => {
  return (
    <View style={styles.container}>
      <WebView
        source={{ uri: iframeUrl }}
        allowsFullscreenVideo={true}
        style={styles.iframe}
        renderLoading={renderLoading} 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    
    backgroundColor:"#000" // Adjust the height as needed
  },
  iframe: {
    flex: 1,
    width: Dimensions.get('window').width,
    backgroundColor:"#000" 
  },
});

export default IframeComponent;
