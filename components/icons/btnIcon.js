import { Image,Pressable } from "react-native";
import { TouchableOpacity } from "react-native-gesture-handler";

const IconButton= ({btnStyle,imgStyle,source,handlePress})=>
{
    return (
        <TouchableOpacity style={btnStyle} onPress={handlePress} onLongPress={handlePress}>
            <Image source={source} style={imgStyle} resizeMode="cover" ></Image>
           
        </TouchableOpacity>
    )
}

export default IconButton;