import * as React from "react";
import {
  View,
  useWindowDimensions,
  StyleSheet,
  ActivityIndicator,
  Text,
  ScrollView,
} from "react-native";
import { TabView, SceneMap, TabBar } from "react-native-tab-view";
import { COLORS } from "../../constants";
import { getAccessToken } from "../../api/auth";
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";


export default function Frequencytab() {
  const layout = useWindowDimensions();
  const [frequency, setFrequency] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [accessToken, setAccessToken] = React.useState(null); // Store data
  const [index, setIndex] = React.useState(0);
  const [routes, setRoutes] = React.useState([
    { key: "first", title: " HD قناة الريان" },
    { key: "second", title: "قناة الريان القديم HD" },
  ]);


  const fetchData = async () => {
    const cachedData = await AsyncStorage.getItem("frequencyData");
    const cachedTimestamp = await AsyncStorage.getItem("frequencyDataTimestamp");
  
    if (cachedData && cachedTimestamp) {
      const currentTime = new Date().getTime();
      const cachedTime = parseInt(cachedTimestamp, 10);
      const expirationTime = 12 * 60 * 60 * 1000; // 12 hours in milliseconds
  
      if (currentTime - cachedTime < expirationTime) {
        // Data is still valid, use it
        setFrequency(JSON.parse(cachedData));
        setLoading(false);
        return;
      }
    }
  
    // Fetch new data if cache is expired or doesn't exist
    const YOUR_API_KEY = "QU03SzE4N3BjZUNNZW1ONXVHeXREblhNWXIyem9oVmpRUnpERkFtdDp5a0VDUE5MRVNPVXlrbURMUVFKRHZRdkJZYmlGTXlOWWsxQ3ZLMTh5";
  
    getAccessToken(YOUR_API_KEY)
      .then((token) => {
        setAccessToken(token);
        axios
          .get("https://alrayyan.tv/wp-json/tvapi/v2/frequency", {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })
          .then((response) => {
            const data = response.data;
            setFrequency(data);
            AsyncStorage.setItem("frequencyData", JSON.stringify(data)); // Cache the data
            AsyncStorage.setItem("frequencyDataTimestamp", String(new Date().getTime())); // Cache the timestamp
            setLoading(false);
          })
          .catch((error) => {
            console.error("Error fetching data:", error);
            setLoading(false);
          });
      })
      .catch((error) => {
        console.error("Error obtaining access token:", error);
        setLoading(false);
      });
  };
  

  React.useEffect(() => {
    // Fetching and Caching the Data using Async STorage 
        fetchData();
     
   }, []);
 
 

  const FirstRoute = () => (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.rowWrapper}>
        <View style={styles.firstrow}>
          <View style={styles.column}>
          <Text style={[styles.firstrowtext,{ writingDirection: "ltr" }]}>
               {frequency[0]?.satellites?.[0]?.satellitename} &#8226;
            </Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}></Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}></Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.text}>التردد</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>Frequency</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>{frequency[0]?.satellites?.[0]?.frequency}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.text}>المدار</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>Orbit</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>{frequency[0]?.satellites?.[0]?.orbit}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.text}>القطبية</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>Polar</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>{frequency[0]?.satellites?.[0]?.polarity}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.text}>معدل الترميز</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>Symbol Rate</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>{frequency[0]?.satellites?.[0]?.symbolrate}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.text}>معامل تصحيح الخطأ</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>FEC</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>{frequency[0]?.satellites?.[0]?.fec}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.text}>الاشارة</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>Signal</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>-</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.text}>التعديل</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>Modification</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>-</Text>
          </View>
        </View>


        <View style={styles.blackfirstrow}>
          <View style={styles.column}>
            <Text style={styles.blackfirstrowtext}>&#8226; {frequency[0]?.satellites?.[1]?.satellitename}</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>-</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.text}>-</Text>
          </View>
        </View>
        <View style={styles.blackrow}>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>التردد</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>Frequency</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>{frequency[0]?.satellites?.[1]?.frequency}</Text>
          </View>
        </View>
        <View style={styles.blackrow}>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>المدار</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>Orbit</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>{frequency[0]?.satellites?.[1]?.orbit}</Text>
          </View>
        </View>
        <View style={styles.blackrow}>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>القطبية</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>Polar</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>{frequency[0]?.satellites?.[1]?.polarity}</Text>
          </View>
        </View>
        <View style={styles.blackrow}>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>معدل الترميز</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>Symbol Rate</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>{frequency[0]?.satellites?.[1]?.symbolrate}</Text>
          </View>
        </View>
        <View style={styles.blackrow}>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>معامل تصحيح الخطأ</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>FEC</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>{frequency[0]?.satellites?.[1]?.fec}</Text>
          </View>
        </View>
   
        <View style={styles.blackrow}>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>الاشارة</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>Signal</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>-</Text>
          </View>
        </View>
        <View style={styles.blackrow}>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>التعديل</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>Modification</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.blackrowtext}>-</Text>
          </View>
        </View>
        
      </View>
    </ScrollView>
  );

  const SecondRoute = () => (
    <ScrollView contentContainerStyle={styles.container}>
    <View style={styles.rowWrapper}>
      <View style={styles.firstrow}>
        <View style={styles.column}>
          <Text style={styles.firstrowtext}>&#8226; {frequency[1]?.satellites?.[0]?.satellitename}</Text>
        </View>
       
        <View style={styles.column}>
          <Text style={styles.text}></Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>التردد</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Frequency</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[0]?.frequency}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>المدار</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Orbit</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[0]?.orbit}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>القطبية</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Polar</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[0]?.polarity}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>معدل الترميز</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Symbol Rate</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[0]?.symbolrate}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>معامل تصحيح الخطأ</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>FEC</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[0]?.fec}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>الاشارة</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Signal</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>-</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>التعديل</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Modification</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>-</Text>
        </View>
      </View>


      <View style={styles.blackfirstrow}>
        <View style={styles.column}>
          <Text style={styles.blackfirstrowtext}>&#8226; {frequency[1]?.satellites?.[1]?.satellitename}</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>-</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>-</Text>
        </View>
      </View>
      <View style={styles.blackrow}>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>التردد</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>Frequency</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>{frequency[1]?.satellites?.[1]?.frequency}</Text>
        </View>
      </View>
      <View style={styles.blackrow}>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>المدار</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>Orbit</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>{frequency[1]?.satellites?.[1]?.orbit}</Text>
        </View>
      </View>
      <View style={styles.blackrow}>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>القطبية</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>Polar</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>{frequency[1]?.satellites?.[1]?.polarity}</Text>
        </View>
      </View>
      <View style={styles.blackrow}>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>معدل الترميز</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>Symbol Rate</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>{frequency[1]?.satellites?.[1]?.symbolrate}</Text>
        </View>
      </View>
      <View style={styles.blackrow}>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>معامل تصحيح الخطأ</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>FEC</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>{frequency[1]?.satellites?.[1]?.fec}</Text>
        </View>
      </View>
 
      <View style={styles.blackrow}>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>الاشارة</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>Signal</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>-</Text>
        </View>
      </View>
      <View style={styles.blackrow}>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>التعديل</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>Modification</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.blackrowtext}>-</Text>
        </View>
      </View>

      <View style={styles.firstrow}>
        <View style={styles.column}>
          <Text style={styles.firstrowtext}>&#8226; {frequency[1]?.satellites?.[2]?.satellitename}</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>-</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>-</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>التردد</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Frequency</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}> {frequency[1]?.satellites?.[2]?.frequency}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>المدار</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Orbit</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[2]?.orbit}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>القطبية</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Polar</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[2]?.polarity}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>معدل الترميز</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Symbol Rate</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[2]?.symbolrate}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>معامل تصحيح الخطأ</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>FEC</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>{frequency[1]?.satellites?.[2]?.fec}</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>الاشارة</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Signal</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>-</Text>
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.text}>التعديل</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>Modification</Text>
        </View>
        <View style={styles.column}>
          <Text style={styles.text}>-</Text>
        </View>
      </View>
      
    </View>
  </ScrollView>
  );

  const renderScene = SceneMap({
    first: FirstRoute,
    second: SecondRoute,
  });

  return (
    <>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={"#fff"} />
        </View>
      ) : (
        <TabView
          navigationState={{ index, routes }}
          renderTabBar={(props) => (
            <TabBar
              style={{
                backgroundColor: COLORS.backgroundColor,
                borderBottomWidth: 3,
                borderColor: COLORS.backgroundColor,
              }}
              indicatorStyle={{ backgroundColor: "#fff" }}
              tabStyle={{ width: 160 }}
              {...props}
            />
          )}
          renderScene={renderScene}
          onIndexChange={setIndex}
          initialLayout={{ width: layout.width }}
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    
    flexGrow: 1,
   
    backgroundColor: "#fff",
  },

  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  rowWrapper: {
    // Border color
    paddingVertical: 10, // Vertical padding
  },
  firstrow: {
    borderWidth: 0,
    flexDirection: "row", // Arrange columns horizontally
    justifyContent: "space-between", // Space evenly between columns
  },
  firstrowtext: {
    color: COLORS.primaryHlColor,
    textAlign: "left",
    fontWeight: "bold",
    fontSize: 16,
  },
  blackfirstrow: {
    borderWidth: 0,
    flexDirection: "row", // Arrange columns horizontally
    justifyContent: "space-between",
     // Space evenly between columns
     backgroundColor:COLORS.backgroundColor
  },
  blackfirstrowtext: {
    color: COLORS.primaryHlColor,
    textAlign: "left",
    fontWeight: "bold",
    fontSize: 16,
  },
  row: {
    flexDirection: "row", // Arrange columns horizontally
    justifyContent: "space-between", // Space evenly between columns
    borderBottomWidth: 1, // Bottom border
    borderTopWidth: 1, // Top border
    borderColor: COLORS.colBorderColor,
  },
  column: {
    flex: 1, // Equal width for each column
    paddingHorizontal: 10, // Horizontal padding
    paddingVertical: 12,
  },
  text: {
    textAlign: "center", // Center-align text in each column
    fontSize: 16,
  },
  blackrow: {
    flexDirection: "row", // Arrange columns horizontally
    justifyContent: "space-between", // Space evenly between columns
    borderBottomWidth: 1, // Bottom border
    borderTopWidth: 1, // Top border
    borderColor: COLORS.colBlackBorderColor,
    backgroundColor:COLORS.backgroundColor
  },
  blackrowtext: {
    textAlign: "center", // Center-align text in each column
    fontSize: 16,
    color:"#fff"
  }
});
