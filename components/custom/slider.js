import React, { useEffect, useState } from 'react';
import { View, Modal, StyleSheet,TouchableOpacity,Platform } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import ImageViewer from 'react-native-image-zoom-viewer';
import { COLORS } from '../../constants';
import { useSafeAreaInsets } from "react-native-safe-area-context";


const SliderModal = ({ images, isVisible, onClose,index }) => {
  const insets = useSafeAreaInsets();
  const top = Platform.OS==="android"?0:insets.top;
    const [imgList,setImgList] = useState(null)

    useEffect(()=>{
        const transformedData = images.map(item => ({
            url: item.image_link,
          }));
          setImgList(transformedData);
    },[images])



    return (
      <Modal visible={isVisible} animationType="slide" >
    {/*     <PagerView style={styles.pagerView} initialPage={0}>
          {images.map((image, index) => (
            <View key={index}>
              <Image style={styles.image} source={{ uri: image.image_link }} />
            </View>
          ))}
        </PagerView>
       */}
        <View style={[styles.header,{marginTop:Platform.OS==='ios'?top+5:0}]}>
        <TouchableOpacity onPress={onClose}>
          <FontAwesome name="close" color={COLORS.textColor} size={24}></FontAwesome>
        </TouchableOpacity>
        </View>
        <ImageViewer imageUrls={imgList} index={index}/>
        
      </Modal>
    );
  };
  
  const styles = StyleSheet.create({
    pagerView: {
      flex: 1,
    },
    image: {
      width: '100%',
      height: '100%',
      resizeMode: 'contain',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        padding: 15,
        zIndex:5,
        backgroundColor:"black"
      },
  });

  export default SliderModal;