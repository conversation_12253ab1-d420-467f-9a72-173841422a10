import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { Video } from 'expo-av';
import { LogBox } from 'react-native';

LogBox.ignoreLogs([
  /.*new NativeEventEmitter\(\) was called with a non-null argument without the required addListener method.*/,
]);
const SplashPlayer = ({ toggleSplash }) => {
  
  const videoRef = useRef(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const handleVideoPlaybackStatusUpdate = (status) => {
    if (status.isLoaded && !status.isPlaying && status.positionMillis > 0) {
      // Video playback completed
      toggleSplash(true);
    }
  };

  useEffect(() => {
    // Use Animated.timing to create a fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000, // Adjust the duration as needed
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  return (
    <Animated.View style={{ ...styles.container, opacity: fadeAnim }}>
      <Video
        ref={videoRef}
        source={require('../../assets/splash/splash.mp4')}
        resizeMode="cover"
        shouldPlay
        isLooping={false}
        onPlaybackStatusUpdate={handleVideoPlaybackStatusUpdate}
        style={styles.video}
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'black',
  },
  video: {
    flex: 1,
    width: '100%',
  },
});

export default SplashPlayer;
