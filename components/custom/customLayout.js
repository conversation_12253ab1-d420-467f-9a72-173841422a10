import React, { useState, useEffect } from "react";
import { I18nManager } from "react-native";
import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import RNRestart from 'react-native-restart';
import { Stack } from "expo-router";
import SplashPlayer from "../../components/custom/splash";
import {useLayout } from '../../context/rtl';
import AsyncStorage from "@react-native-async-storage/async-storage";



I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const CustomLayout = () => {
  const { state, dispatch } = useLayout();
  const [fontsLoaded] = useFonts({
    GEuniLight: require("../../assets/fonts/GC¼GE-SS-Unique-Light.ttf"),
    GELight: require("../../assets/fonts/GE-SS-Text-Light.ttf"),
    GERegular: require("../../assets/fonts/GE-SS-Text-Medium.ttf"),
    GEBold: require("../../assets/fonts/GE-SS-Two-Bold-AR-EN.ttf"),
  });
  const [splash, setSplash] = useState(true);
  


  useEffect(() => {
    async function prepare() {
    
      await SplashScreen.preventAutoHideAsync();
    }
    async function storeItem(dir)
    {
      await AsyncStorage.setItem("dir",dir);
    }
    prepare();

    if (state.layout === 'ltr') {
      dispatch({ type: 'SET_LAYOUT_DIRECTION', direction: 'rtl' });
      storeItem("rtl");
     RNRestart.restart();
     
    }

    if (state.layout === 'rtl') {
      // Handle RTL-specific actions
    }
  }, [state.layout, dispatch]);

  const toggleSplash = () => {
    setSplash(false);
  }

  useEffect(() => {
    if (fontsLoaded && state.layout === 'rtl' ) {
      SplashScreen.hideAsync();
     
    }
  }, [fontsLoaded,state.layout]);


  useEffect(() => {
    // ComponentDidMount actions

  }, [state.layout]);

  if (splash ) {
    return (<SplashPlayer toggleSplash={toggleSplash} />);
  }

  return (
   
  <Stack initialRouteName="(tabs)">
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
    </Stack>
   
  
  );
};

export default CustomLayout;
