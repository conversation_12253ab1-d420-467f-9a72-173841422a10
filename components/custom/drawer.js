import { FontAwesome } from '@expo/vector-icons';
import { router,useNavigation,usePathname, useSegments } from 'expo-router';
import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, PanResponder, Dimensions, Modal, TouchableOpacity, Image, ImageBackground,Platform } from 'react-native';
import { COLORS, FONT, TEXTDATA } from '../../constants';
import { useSafeAreaInsets } from "react-native-safe-area-context";

const ModalSlideFromRight = ({ visible, onClose }) => {
  const screenWidth = Dimensions.get('window').width;
  const animationValue = useRef(new Animated.Value(screenWidth)).current;
  const navigation = useNavigation();
  const currentRoute =usePathname();
  const insets = useSafeAreaInsets();
  const top = Platform.OS==="android"?0:insets.top;
 
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return visible && gestureState.dx < -10; // Recognize swipes from right to left only when modal is visible
      },
      onPanResponderMove: (_, gestureState) => {
        // Update the modal position based on swipe gesture
        animationValue.setValue(gestureState.dx);
      },
      onPanResponderRelease: (_, gestureState) => {
        // Determine whether to close the modal based on swipe distance
        if (gestureState.dx < -50 || gestureState.dx > 50) {
          onClose();
        } else {
          // Snap the modal back to the open position
          Animated.spring(animationValue, {
            toValue: 0,
            stiffness: 300,
            damping: 30,
            useNativeDriver: false,
          }).start();
        }
      },
    })
  ).current;

  const handleTouch = (nav)=>
  {
   
    onClose();
    const currentRouteName = currentRoute;
 
    
    if(currentRouteName!=="/"+nav)
    {
      const nextRoute = "/"+nav;
      if(nextRoute ==="/shows")
      {
        router.push('/shows')
      }
      else
      {
        navigation.navigate(nav);
      }
     

    }
   
  }
  // Animate the modal whenever the 'visible' prop changes
  useEffect(() => {
    if (visible) {
      Animated.timing(animationValue, {
        toValue: 0,
        duration: 400,
        useNativeDriver: false,
      }).start();
    } else {
      // If the modal becomes invisible, reset its position
      Animated.timing(animationValue, {
        toValue: screenWidth,
        duration: 1, // Set to 1 millisecond to reset immediately
        useNativeDriver: false,
      }).start();
    }
  }, [visible]);

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={visible}
      onRequestClose={() => onClose()}
      statusBarTranslucent={true}
    >
      <View
        style={styles.modalBackground}
       
      >
        <ImageBackground  source={require('../../assets/layer_3.jpg')}
          style={styles.backgroundImage}>
            <View style={styles.overlay}></View>
        <Animated.View
          {...panResponder.panHandlers}
          style={[
            styles.modalContainer,
            {
              transform: [{ translateX: animationValue }],
            },
          ]}
        >
          
            <TouchableOpacity onPress={() => onClose()} style={[styles.closeButton,{top:Platform.OS==='android'?top+15:top+5}]}>
              <FontAwesome name="close" color ="white" size={24}></FontAwesome>
            </TouchableOpacity>
           
 
          <View style={styles.menuContainer}>
            <TouchableOpacity onPress={()=>{handleTouch("home")}} style={currentRoute==="/home"?styles.activeTouchArea:styles.touchArea}><Text style={styles.menuItem}>{TEXTDATA.homeTitlebarName}</Text></TouchableOpacity>
            <View style={styles.menuDivider}></View>
            <TouchableOpacity  onPress={()=>{handleTouch("frequency")}} style={currentRoute==="/frequency"?styles.activeTouchArea:styles.touchArea}><Text style={styles.menuItem}>{TEXTDATA.frequencyTitlebarName}</Text></TouchableOpacity>
            <View style={styles.menuDivider}></View>
            <TouchableOpacity  onPress={()=>{handleTouch("latest")}} style={currentRoute==="/latest"?styles.activeTouchArea:styles.touchArea}><Text style={styles.menuItem}>{TEXTDATA.latestTitlebarName}</Text></TouchableOpacity>
            <View style={styles.menuDivider}></View>
            <TouchableOpacity  onPress={()=>{handleTouch("contest")}} style={currentRoute==="/contest"?styles.activeTouchArea:styles.touchArea}><Text style={styles.menuItem}>{TEXTDATA.contestTitlebarName}</Text></TouchableOpacity>
            <View style={styles.menuDivider}></View>
            <TouchableOpacity  onPress={()=>{handleTouch("shows")}} style={currentRoute==="/shows"?styles.activeTouchArea:styles.touchArea}><Text style={styles.menuItem}>{TEXTDATA.showsTitlebarName}</Text></TouchableOpacity>
            <View style={styles.menuDivider}></View>
            <TouchableOpacity  onPress={()=>{handleTouch("news")}} style={currentRoute==="/news"?styles.activeTouchArea:styles.touchArea}><Text style={styles.menuItem}>{TEXTDATA.newsTitlebarName}</Text></TouchableOpacity>
            <View style={styles.menuDivider}></View>
            <TouchableOpacity  onPress={()=>{handleTouch("album")}} style={currentRoute==="/album"?styles.activeTouchArea:styles.touchArea}><Text style={styles.menuItem}>{TEXTDATA.albumTitlebarName}</Text></TouchableOpacity>
            <View style={styles.menuDivider}></View>
            <TouchableOpacity  onPress={()=>{handleTouch("contact")}} style={currentRoute==="/contact"?styles.activeTouchArea:styles.touchArea}><Text style={styles.menuItem}>{"تواصل معنا"}</Text></TouchableOpacity>
            <View style={styles.menuDivider}></View>
          </View>
        </Animated.View>
        </ImageBackground>
      
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor:"#fff"
  },
  touchArea:
  {
    width:"100%",
    maxWidth:250,
    alignContent:"center",
    justifyContent:"center",
    textAlign:"center",
   
  },
  activeTouchArea:
  {
    width:"100%",
    maxWidth:250,
    alignContent:"center",
    justifyContent:"center",
    textAlign:"center",
    backgroundColor:COLORS.primaryHlColor
  },
  backgroundImage: {
    width:"100%",
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor:"rgba(255,255,255,0.5)"
  },
  overlay: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.5)', // White overlay with some opacity
  },
  modalContainer: {
    width: '70%', // Adjust the width as needed
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.8)', // Dark background with high opacity
    paddingVertical:"15%"
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingLeft: 20,
  },
  closeButton: {
    position: 'absolute',
    left: 20, // Adjust the left position as needed
    padding: 10,
    zIndex:5
  },
  menuContainer: {
    alignItems: 'center',
  },
  menuItem: {
    color: 'white',
    fontSize: 20,
    paddingVertical: 20,
    fontFamily:FONT.medium,
    textAlign:"center"
  },
  menuDivider: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.3)',
    width: '90%', // Same as modal width
  },

  activeMenuDivider: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.primaryHlColor,
    width: '90%', // Same as modal width
  },
});

export default ModalSlideFromRight;
