import React, { useState } from 'react';
import { View, Text, ScrollView, TextInput, Button, StyleSheet, TouchableOpacity,Alert,ActivityIndicator } from 'react-native';
import { RadioButton } from 'react-native-paper';
import * as DocumentPicker from 'expo-document-picker';
import { COLORS, TEXTDATA } from '../../constants';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import axios from "axios";
import * as Sentry from 'sentry-expo';



export default function ContactForm() {
  const [selectedEmail, setSelectedEmail] = useState('Finance'); // Default to Finance
  const [dateValid,setDateValid] = useState(true);
  const [emailValid,setEmailValid] = useState(true);
  const [phoneValid,setPhoneValid] = useState(true);

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    showName: '',
    dateOfEpisode: '',
  });

  const [attachments, setAttachments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fileNames, setFileNames] = useState([]);
  const subject ='البريد من تطبيق قناة الريان';
  const currentDate = new Date();

const day = String(currentDate.getDate()).padStart(2, '0'); // Get day and pad with leading zero if needed
const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Get month (0-based) and pad with leading zero if needed
const year = currentDate.getFullYear();

const formattedDate = `${day}-${month}-${year}`;

const final_subject = formattedDate+" - "+subject;
  const handleEmailChange = (email) => {
    setSelectedEmail(email);
  };

  const isEmailValid = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  const isPhoneNumberValid = (phoneNumber) => /^\d{8,10}$/.test(phoneNumber);
  const isDateValid = (date) => {
    // Regular expression to match different date formats
    const dateFormats = [
      /^\d{2}-\d{2}-\d{4}$/, // dd-mm-yyyy
      /^\d{4}-\d{2}-\d{2}$/, // yyyy-mm-dd
      /^\d{2}\/\d{2}\/\d{4}$/, // dd/mm/yyyy
      /^\d{4}\/\d{2}\/\d{2}$/, // yyyy/mm/dd
    ];
  
    // Check if the input matches any of the valid formats
    for (const format of dateFormats) {
      if (format.test(date)) {
        return true;
      }
    }
  
    // If no valid format is found, return false
    return false;
  };
  


  const selectEmailToSend=(item)=>
  {
    if(item==="Finance")
    {
      return "<EMAIL>";
    }
    else if(item==="Social Media")
    {
       return "<EMAIL>";
    }
    else
    {
      return "<EMAIL>"
    }
  } 

  const sendEmail = async () => {
    setLoading(true);

    if (!isEmailValid(formData.email)) {
      Alert.alert('خطأ', 'يرجى إدخال عنوان بريد إلكتروني صالح',[
        {
          text: 'تمام',
          style: 'cancel',
        }
      ]);
      setLoading(false);
      return;
    }
    if (!isPhoneNumberValid(formData.phone)) {
      Alert.alert('خطأ', 'يرجى إدخال رقم هاتف صالح',[
        {
          text: 'تمام',
          style: 'cancel',
        }
      ]);
      setLoading(false);
      return;
    }
    if (!isDateValid(formData.dateOfEpisode)) {
      Alert.alert('خطأ', 'ارجوك ادخل تاريخ صحيح',[
        {
          text: 'تمام',
          style: 'cancel',
        }
      ]);
      setLoading(false);
      return;
    }
    const emailData = new FormData();
    emailData.append('name', formData.name);
    emailData.append('phone', formData.phone);
    emailData.append('email', formData.email);
    emailData.append('showName', formData.showName);
    emailData.append('dateOfEpisode', formData.dateOfEpisode);
    emailData.append('to', selectEmailToSend(selectedEmail));
    emailData.append('from', '<EMAIL>'); // Replace with the sender's email
    emailData.append('subject', final_subject);
  
    attachments.forEach((attachment, index) => {
      emailData.append('attachments', {
        uri: attachment,
        type: 'application/octet-stream', // Set the appropriate MIME type for your file
        name: fileNames[index],
      });
    });
  
    try {
      const response = await axios.post('https://cc10-3-16-125-140.ngrok-free.app/email/send', emailData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'ngrok-skip-browser-warning':'8568'
        },
      });
  
      console.log('Email sent successfully:', response.data);
      // Handle success as needed
      setLoading(false);
      Alert.alert('نجاح', 'تم ارسال البريد الالكتروني بنجاح',[
        {
          text: 'تمام',
          style: 'cancel',
        }
      ]);
    } catch (error) {
      Sentry.Native.captureException(error);
      console.log('Error sending email:', error);
      setLoading(false);
      // Handle the error as needed
      Alert.alert('خطأ', 'غير قادر على إرسال البريد الإلكتروني في الوقت الراهن',[
        {
          text: 'تمام',
          style: 'cancel',
        }
      ]);
    }
  
    setSelectedEmail('Finance');
    setFormData({
      name: '',
      phone: '',
      email: '',
      showName: '',
      dateOfEpisode: '',
    });
    setAttachments([]);
    setFileNames([]);
  };

  const handleAttachmentPick = async (index) => {
   
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple:false,
      });
      if (!result.canceled) {
        if(attachments.length<3)
        {
          const newAttachmentUri = result.assets[0].uri;
          const newAttachmentName = result.assets[0].name;
           setAttachments([...attachments, newAttachmentUri]);
           setFileNames([...fileNames,newAttachmentName]);
        }
        else
        {
          setAttachments(prevAttachments => [
            ...prevAttachments.slice(0, index - 1),
            result.assets[0].uri,
            ...prevAttachments.slice(index)
          ]);
          
          setFileNames(prevFileNames => [
            ...prevFileNames.slice(0, index - 1),
            result.assets[0].name,
            ...prevFileNames.slice(index)
          ]);
        }
       
      }


    
    
  

    
  };

  const renderAttachmentButtons = () => {
    return (
      <View style={styles.attachmentButtonsContainer}>
      
        <TouchableOpacity style={styles.attachmentButton} onPress={()=>{handleAttachmentPick(1)}}>
         
          <Text style={styles.attachmentButtonText}>{attachments.length>=1?fileNames[0]:TEXTDATA.contactTenthName+" |"}</Text>
          <MaterialCommunityIcons name="upload" size={24} color="black"></MaterialCommunityIcons>
        </TouchableOpacity>
        <TouchableOpacity style={styles.attachmentButton} onPress={()=>{handleAttachmentPick(2)}}>
        <Text style={styles.attachmentButtonText}>{attachments.length>=2?fileNames[1]:TEXTDATA.contactTenthName+" |"}</Text>
          <MaterialCommunityIcons name="upload" size={24} color="black"></MaterialCommunityIcons>
        </TouchableOpacity>
        <TouchableOpacity style={styles.attachmentButton} onPress={()=>{handleAttachmentPick(3)}}>
        <Text style={styles.attachmentButtonText}>{attachments.length===3?fileNames[2]:TEXTDATA.contactTenthName+" |"}</Text>
          <MaterialCommunityIcons name="upload" size={24} color="black"></MaterialCommunityIcons>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    
    
    
    <ScrollView contentContainerStyle={styles.container}>
    
   { loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.backgroundColor} />
        </View>
      ):
      <View style={{flex:1}}>
             <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <RadioButton
          value="Finance"
          status={selectedEmail === 'Finance' ? 'checked' : 'unchecked'}
          onPress={() => handleEmailChange('Finance')}
          
        />
        <Text>{TEXTDATA.contactFirstName}</Text>
      </View>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <RadioButton
          value="Social Media"
          status={selectedEmail === 'Social Media' ? 'checked' : 'unchecked'}
          onPress={() => handleEmailChange('Social Media')}
        />
        <Text>{TEXTDATA.contactSecondName}</Text>
      </View>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <RadioButton
          value="Feedback"
          status={selectedEmail === 'Feedback' ? 'checked' : 'unchecked'}
          onPress={() => handleEmailChange('Feedback')}
        />
        <Text>{TEXTDATA.contactThirdName}</Text>
      </View>

      <View style={styles.textInputsContainer}>
        <TextInput
          style={styles.textFirstInput}
          placeholder={TEXTDATA.contactFourthName}
          value={formData.name}
          onChangeText={(text) => setFormData({ ...formData, name: text })}
        />
        <TextInput
          style={phoneValid?styles.textInput:styles.errorInput}
          keyboardType='numeric'
          placeholder={TEXTDATA.contactFifthName}
          value={formData.phone}
          onChangeText={(text) => setFormData({ ...formData, phone: text })}
          onBlur={() => {
            if (formData.phone && !isPhoneNumberValid(formData.phone)) {
              setPhoneValid(false);
              Alert.alert('خطأ', 'يرجى إدخال رقم هاتف صالح',[
                {
                  text: 'تمام',
                  style: 'cancel',
                }
              ]);
            }
            else
            {
              setPhoneValid(true);
            }
          }}
        />
        <TextInput
          style={emailValid?styles.textInput:styles.errorInput}
          placeholder={TEXTDATA.contactSixName}
          value={formData.email}
          onChangeText={(text) => setFormData({ ...formData, email: text })}
          onBlur={() => {
            if (formData.email && !isEmailValid(formData.email)) {
              setEmailValid(false);
              Alert.alert('خطأ', 'يرجى إدخال عنوان بريد إلكتروني صالح',[
                {
                  text: 'تمام',
                  style: 'cancel',
                }
              ]);
            }
            else
            {
              setEmailValid(true);
            }
          }}
        />
        <TextInput
          style={styles.textInput}
          placeholder={TEXTDATA.contactSeventhName}
          value={formData.showName}
          onChangeText={(text) => setFormData({ ...formData, showName: text })}
        />
        <TextInput
          style={dateValid?styles.textInput:styles.errorInput}
          placeholder={TEXTDATA.contactEightName}
          value={formData.dateOfEpisode}
          onChangeText={(text) => setFormData({ ...formData, dateOfEpisode: text })}
          onBlur={() => {
            if (formData.dateOfEpisode && !isDateValid(formData.dateOfEpisode)) {
              setDateValid(false);
              Alert.alert('خطأ', 'الرجاء إدخال التاريخ بالصيغة dd-mm-yyyy',[
                {
                  text: 'تمام',
                  style: 'cancel',
                }
              ]);
            }
            else
            {
              setDateValid(true);
            }
          }}
        />
      
       
       
      </View>
      <View style={styles.separator}>
        <View style={{ textAlign: 'right' }}>
          <Text style={styles.separatorText}>{TEXTDATA.contactNinthName}</Text>
        </View>
      </View>
      {renderAttachmentButtons()}

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.buttonSubmit} onPress={sendEmail}>
            <Text style={styles.buttonText}>{TEXTDATA.contactTitlebarName}</Text>
        </TouchableOpacity>
      </View>

      </View>
    
    }
   
     
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  errorInput:{
   borderBottomColor:"red",
   borderBottomWidth: 1,
   paddingVertical: 10,
   paddingRight: 16,
   textAlign: 'right', // Align text to the right
  },
  container: {
    padding: 16,
    flex:1,
    backgroundColor:"#fff"

  },
  textInputsContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
    marginVertical:5
  },
  textInput: {
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
    paddingVertical: 10,
    paddingRight: 16,
    textAlign: 'right', // Align text to the right
  },
  textFirstInput:
  {
    borderTopWidth: 1,
    borderTopColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
    paddingVertical: 14,
    paddingRight: 16,
    textAlign: 'right', // Align text to the right
  },
  attachmentButtonsContainer: {
    flex:1,
    
    marginVertical: 16,
  },
  attachmentButton: {
  
    backgroundColor: 'transparent',
    width:160,
    height:40,
    borderRadius: 10,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginVertical: 8,
    flexDirection: 'row', // Align icon and text horizontally
    alignItems: 'center',
    borderColor:"black",
    borderWidth:1
  },
  attachmentButtonText: {
    textAlign: "left",
    padding:2,
    color: COLORS.textSecondColor
  },
  separator: {
  
    alignItems: 'flex-start', // Align text to the left
  },
  separatorText: {
    textAlign: 'right', // Make the separator text right-to-left (RTL)
  },
  buttonContainer:
  {
    alignItems:"center",
    justifyContent:"center"
  },
  buttonSubmit:
  {
    backgroundColor:COLORS.primaryHlColor,
    color:COLORS.textColor,
    borderRadius:25,
    borderWidth:1

  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
   
  },
  buttonText:
  {
    color:COLORS.textColor,
    padding:8,
    width:160,
    height:40,
    textAlign:"center",
    fontSize:16
  }
});
