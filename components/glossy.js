import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

const GlossyButton = () => {
  return (
    <TouchableOpacity style={styles.button}>
     <LinearGradient
            colors={['rgba(0, 0, 0, 0.15)','rgba(255, 255, 255, 0.65)','rgba(255, 255, 255, 0.55)','rgba(255, 255, 255, 0.45)','rgba(255, 255, 255, 0.25)','rgba(255, 255, 255, 0.15)','rgba(255, 255, 255, 0.27)']}
            style={styles.gloss}
          />
          <Text style={styles.buttonText}>Click Me</Text>
          <LinearGradient
            colors={['rgba(149, 30, 54,0.10)','rgba(149, 30, 54,0.25)']}
            style={styles.glossTop}
          />    
     
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#951e36', // Your desired color
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5, // Shadow effect on Android
    padding:6,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    
  },

  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    top:'3%'
  },
  gloss: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: '40%', // Adjust this value to control the gloss effect's size
  },
  glossTop: {
    position: 'absolute',
    top: '60%',
    left: 0,
    right: 0,
    bottom: 0, // Adjust this value to control the gloss effect's size
  },
});

export default GlossyButton;
