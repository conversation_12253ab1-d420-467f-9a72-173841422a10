import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import axios from "axios";
import { getAccessToken } from "../../api/auth";
import { COLORS, TEXTDATA, NUMBERS, FONT } from "../../constants";
import { useRouter } from "expo-router";
import { useAlbum } from "../../context/album";
import AsyncStorage from "@react-native-async-storage/async-storage";

const AlbumList = () => {
  const [albums, setAlbums] = useState([]);
  const [loading, setLoading] = useState(true);
  const [numColumns, setnumColumns] = useState(2);
  const [orientation, setOrientation] = useState("");
  const [accessToken, setAccessToken] = useState(null); // Stor
  const [uniAlbum, setUniAlbum] = useState([]);
  const { toggleAlbum } = useAlbum();
  const router = useRouter();
  useEffect(() => {
    // Fetch the access token first

    getOrientation();
    fetchData();
  }, []);

  // Define a function to fetch and cache the data
  // Function to fetch data and update the cache
  const fetchData = async () => {
    const YOUR_API_KEY =
      "QU03SzE4N3BjZUNNZW1ONXVHeXREblhNWXIyem9oVmpRUnpERkFtdDp5a0VDUE5MRVNPVXlrbURMUVFKRHZRdkJZYmlGTXlOWWsxQ3ZLMTh5"; // Replace with your actual API key
    try {
      const cachedData = await AsyncStorage.getItem("albumData");
      const cachedTimestamp = await AsyncStorage.getItem("albumDataTimestamp");

      if (cachedData && cachedTimestamp) {
        const currentTime = new Date().getTime();
        const cachedTime = parseInt(cachedTimestamp, 10);
        const expirationTime = 30 * 60 * 1000; // 12 hours in milliseconds

        if (currentTime - cachedTime < expirationTime) {
          // Data is still valid, use it
          const cachedAlbums = JSON.parse(cachedData);

          // Filter and create unique items from cached data
          const uniqueItems = cachedAlbums.reduce((acc, current) => {
            const { album_id } = current;

            if (!acc[album_id]) {
              acc[album_id] = current;
            }

            return acc;
          }, {});

          setUniAlbum(Object.values(uniqueItems));
          setAlbums(cachedAlbums);
          setLoading(false);
          return;
        }
      }

      // Fetch new data if cache is expired or doesn't exist
      const token = await getAccessToken(YOUR_API_KEY);
      setAccessToken(token);

      const response = await axios.get(
        "https://alrayyan.tv/wp-json/tvapi/v2/album",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const data = response.data;

      // Update the cache with the fetched data
      await AsyncStorage.setItem("albumData", JSON.stringify(data));
      await AsyncStorage.setItem(
        "albumDataTimestamp",
        String(new Date().getTime())
      );

      const uniqueItems = data.reduce((acc, current) => {
        const { album_id } = current;

        if (!acc[album_id]) {
          acc[album_id] = current;
        }

        return acc;
      }, {});

      setUniAlbum(Object.values(uniqueItems));
      setAlbums(data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching data:", error);
      setLoading(false);
    }
  };

  const getOrientation = () => {
    if (Dimensions.get("window").height < Dimensions.get("window").width) {
      if (Dimensions.get("window").width >= 1200) {
        setOrientation("LANDSCAPE_ALT");
        setnumColumns(6);
      } else if (Dimensions.get("window").width >= 1100) {
        setOrientation("LANDSCAPE_LOW_ALT");
        setnumColumns(5);
      } else {
        setOrientation("LANDSCAPE");
        setnumColumns(4);
      }
    } else {
      setOrientation("PORTRAIT");
      setnumColumns(2);
    }
    return orientation;
  };

  const renderAlbumItem = ({ item }) => {
    const displayShow = (item) => {
      const filteredData = albums.filter(
        (elem) => elem.album_id === item.album_id
      );

      toggleAlbum(filteredData);

      router.push("/album/" + item.album_title);
    };
    return (
      <TouchableOpacity
        key={item.id}
        onPress={() => {
          displayShow(item);
        }}
      >
        <View style={styles.albumItem}>
          <Image
            source={{ uri: item.image_link }}
            style={styles.logo}
            resizeMode='cover'
            alt={item.album_title}
          />
          <Text
            style={styles.albumTitle}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {item.album_title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.textColor} />
        </View>
      ) : (
        <FlatList
          key={orientation}
          data={uniAlbum}
          renderItem={renderAlbumItem}
          keyExtractor={(item) => item.id.toString()}
          numColumns={numColumns}
          columnWrapperStyle={styles.flatItem}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  flatItem: {
    alignItems: "center",
    justifyContent: "center",
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundColor,
    padding: 5,
    height: "100%",
    width: "100%",
    alignItems: "center",
  },
  albumItem: {
    borderRadius: 10, // Add rounded corners
    borderWidth: 1, // Add border,
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    margin: 4,
    width: Dimensions.get('screen').width/2.2,
    borderColor: "rgba(0,0,0,0.1)",
  },
  gradient: {
    borderRadius: 10,
    borderWidth: 1, // Add border,
  },
  logo: {
    width: "100%", // Take the full width of the container
    height:(Dimensions.get('screen').width/2.2*9)/16,
    borderRadius: 10,
    borderWidth: 1,

    alignItems: "center",
    alignContent: "center",
  },
  albumTitle: {
    fontSize: 14,
    marginTop: 8,
    color: COLORS.textColor,
    textAlign: "center",
    fontFamily: FONT.medium,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default AlbumList;
