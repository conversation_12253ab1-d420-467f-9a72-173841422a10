import React, { useEffect, useState } from "react";
import {
  View,
  Image,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { COLORS, FONT } from "../../constants";
import { useRouter } from "expo-router";


const ImageList = ({images,toggleModal})=>
{
    const [albums, setAlbums] = useState([images]);
    const [loading, setLoading] = useState(true);
    const [numColumns, setnumColumns] = useState(2);
    const [orientation, setOrientation] = useState("");
  
    const router = useRouter();
    useEffect(() => {
        // Fetch the access token first
        setAlbums(images)
        getOrientation();
        setLoading(false);
      
      }, [images]);
    
      const getOrientation = () => {
        if (Dimensions.get("window").height < Dimensions.get("window").width) {
          if (Dimensions.get("window").width >= 1200) {
            setOrientation("LANDSCAPE_ALT");
            setnumColumns(6);
          } else if (Dimensions.get("window").width >= 1100) {
            setOrientation("LANDSCAPE_LOW_ALT");
            setnumColumns(5);
          } else {
            setOrientation("LANDSCAPE");
            setnumColumns(4);
          }
        } else {
          setOrientation("PORTRAIT");
          setnumColumns(2);
        }
        return orientation;
      };

      const renderAlbumItem = ({ item }) => {

        const displayShow = (id) => {
           toggleModal(id);
       
        };
        return (
          <TouchableOpacity
            key={item.id}
            onPress={() => {
              displayShow(item.id);
            }}
          >
            <View style={styles.albumItem}>
             
              <Image
                source={{ uri: item.image_link }}
                style={styles.logo}
                alt={item.album_title}
              />
              {/* <Text style={styles.albumTitle} numberOfLines={2} ellipsizeMode="tail">{item.album_title}</Text> */}
            </View>
          </TouchableOpacity>
        );
      
      };

      return (
        <View style={styles.container}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.textColor} />
          </View>
        ) : (
          <FlatList
          key={orientation}
          data={albums}
          renderItem={renderAlbumItem}
          keyExtractor={(item) => item.id.toString()}
          numColumns={numColumns}
          columnWrapperStyle={styles.flatItem}
        />
        )}
      </View>
      
      );
}


const styles = StyleSheet.create({
  flatItem: {
    alignItems: "center",
    justifyContent: "center",
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundColor,
    padding: 5,
    height: "100%",
    width: "100%",
    alignItems: "center",
  },
  albumItem: {
    borderRadius: 10, // Add rounded corners
    borderWidth: 1, // Add border,
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    margin: 4,
    width: Dimensions.get('screen').width/2.2,
    borderColor:"rgba(0,0,0,0.1)"
  },
  gradient: {
    borderRadius: 10,
    borderWidth: 1, // Add border,
  
  },
  logo: {
    width: "100%", // Take the full width of the container
    height: (Dimensions.get('screen').width/2.2*9)/16,
    borderRadius: 10,
    borderWidth: 1,
    
    alignItems: "center",
    alignContent: "center",
  
  
  },
  albumTitle: {
    fontSize: 14,
    marginTop: 8,
    color: COLORS.textColor,
    textAlign:"center",
    fontFamily:FONT.medium
  },
 

  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },

 
});

export default ImageList