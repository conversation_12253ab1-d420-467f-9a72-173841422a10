import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import axios from "axios";
import { TabView } from "react-native-tab-view";
import { LinearGradient } from "expo-linear-gradient";
import { getAccessToken } from "../../api/auth";
import { COLORS, TEXTDATA, NUMBERS, FONT } from "../../constants";
import { useRouter } from "expo-router";
import { useShow } from "../../context/shows";
import AsyncStorage from "@react-native-async-storage/async-storage";

const customStyle = StyleSheet.flatten([
  { borderWidth: 1, borderRadius: 10 },
  StyleSheet.absoluteFillObject,
]);
const router = useRouter();


const ProgramList = () => {
  const [programs, setPrograms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [index, setIndex] = useState(0);
  const {toggleShow} =useShow();
  const [numColumns, setnumColumns] = useState(2);
  const [orientation, setOrientation] = useState("");
  const [routes] = useState([
  
    { key: "daily", title: TEXTDATA.tabMenuSecond },
    { key: "kids", title: TEXTDATA.tabMenuThird },
    { key: "events", title: TEXTDATA.tabMenuFourth },
    { key: "weekly", title: TEXTDATA.tabMenuFirst },

  ]);
  const [accessToken, setAccessToken] = useState(null); // Stor

  useEffect(() => {
    // Fetch the access token first
    // Replace with your actual API key
    getOrientation();
    fetchData();
    
  }, []);


  const fetchData = async () => {
    const YOUR_API_KEY =
      "QU03SzE4N3BjZUNNZW1ONXVHeXREblhNWXIyem9oVmpRUnpERkFtdDp5a0VDUE5MRVNPVXlrbURMUVFKRHZRdkJZYmlGTXlOWWsxQ3ZLMTh5"; 
    try {
      const cachedData = await AsyncStorage.getItem("programData");
      const cachedTimestamp = await AsyncStorage.getItem("programDataTimestamp");
  
      if (cachedData && cachedTimestamp) {
        const currentTime = new Date().getTime();
        const cachedTime = parseInt(cachedTimestamp, 10);
        const expirationTime = 30 * 60 * 1000; // 12 hours in milliseconds
  
        if (currentTime - cachedTime < expirationTime) {
          // Data is still valid, use it
          const cachedPrograms = JSON.parse(cachedData);
          setPrograms(cachedPrograms);
          setLoading(false);
          return;
        }
      }
  
      // Fetch new data if cache is expired or doesn't exist
      const token = await getAccessToken(YOUR_API_KEY);
      setAccessToken(token);
  
      const response = await axios.get("https://alrayyan.tv/wp-json/tvapi/v2/programs", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
  
      const data = response.data;
  
      // Update the cache with the fetched data
      await AsyncStorage.setItem("programData", JSON.stringify(data));
      await AsyncStorage.setItem("programDataTimestamp", String(new Date().getTime()));
  
      setPrograms(data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching data:", error);
      setLoading(false);
    }
  };

  const getOrientation = () => {
    if (Dimensions.get("window").height < Dimensions.get("window").width) {
      if (Dimensions.get("window").width >= 1200) {
        setOrientation("LANDSCAPE_ALT");
        setnumColumns(6);
      } else if (Dimensions.get("window").width >= 1100) {
        setOrientation("LANDSCAPE_LOW_ALT");
        setnumColumns(5);
      } else {
        setOrientation("LANDSCAPE");
        setnumColumns(4);
      }
    } else {
      setOrientation("PORTRAIT");
      setnumColumns(2);
    }
    return orientation;
  };
  const TabBarComponent = ({ state, jumpTo }) => {
    return (
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const isFocused = state.index === index;
          const tabStyle = {
            backgroundColor: isFocused
              ? COLORS.primaryHlColor
              : COLORS.ttabBackgroundColor, // Change the background color
            padding: 3,
            width: Dimensions.get('screen').width/4.3,
            height: 40,
            alignItems: "center",
            justifyContent: "center",
            borderRadius: 0, // Set your preferred border radius
            borderWidth: 1,
            borderColor: COLORS.textColor,
            fontSize: 12,
          };

          return (
            <TouchableWithoutFeedback
              key={route.key}
              onPress={() => {
                jumpTo(route.key);
              }}
            >
              <View style={tabStyle}>
                <Text
                  style={{
                    color: isFocused ? COLORS.textColor : COLORS.textColor,
                    fontSize: 13,
                  }}
                  numberOfLines={1} // Limit the text to one line
                  ellipsizeMode="tail"
                >
                  {route.title}
                </Text>
              </View>
            </TouchableWithoutFeedback>
          );
        })}
      </View>
    );
  };

  const renderProgramItem = ({ item }) => {

    const displayShow = (item) => {
       toggleShow(item);
  
      router.push("/shows/" + item.title);
    };
    return (
      <TouchableOpacity
        key={item.id}
        onPress={() => {
          displayShow(item);
        }}
      >
        <View style={styles.programItem}>
          <LinearGradient
            colors={["#8b8b8b", "#333233"]}
            start={{ x: NUMBERS.zero, y: NUMBERS.zero }}
            end={{ x: NUMBERS.zero, y: NUMBERS.one }}
            style={customStyle}
          />
       
          <Image
            source={{ uri: item.logo_url }}
            style={styles.logo}
            alt={item.title}
          />
        </View>
      </TouchableOpacity>
    );
  
  };
  

  const renderScene = ({ route }) => {
    switch (route.key) {
      case "weekly":
        return (
          <FlatList
            key={orientation}
            data={programs.filter(
              (program) =>
                program.category === "البرامج الأسبوعية-Weekly programs"
            )}
            renderItem={renderProgramItem}
            keyExtractor={(item) => item.id.toString()}
            numColumns={numColumns}
            columnWrapperStyle={styles.flatItem}
          />
        );
      case "daily":
        return (
          <FlatList
            key={orientation}
            data={programs.filter(
              (program) => program.category === "البرامج اليومية-Daily Programs"
            )}
            renderItem={renderProgramItem}
            keyExtractor={(item) => item.id.toString()}
            numColumns={numColumns}
            columnWrapperStyle={styles.flatItem}
          />
        );
      case "kids":
        return (
          <FlatList
            key={orientation}
            data={programs.filter((program) => program.category === "الأطفال")}
            renderItem={renderProgramItem}
            keyExtractor={(item) => item.id.toString()}
            numColumns={numColumns}
            columnWrapperStyle={styles.flatItem}
          />
        );
      case "events":
        return (
          <FlatList
            key={route.key + " " + orientation}
            data={programs.filter(
              (program) =>
                program.category !== "البرامج الأسبوعية-Weekly programs" &&
                program.category !== "الأطفال" &&
                program.category !== "البرامج اليومية-Daily Programs"
            )}
            renderItem={renderProgramItem}
            keyExtractor={(item) => item.id.toString()}
            numColumns={numColumns}
            columnWrapperStyle={styles.flatItem}
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.textColor} />
        </View>
      ) : (
        <TabView
          navigationState={{ index, routes }}
          renderScene={renderScene}
          onIndexChange={setIndex}
          renderTabBar={(props) => (
            <TabBarComponent
              navigation={props.navigation}
              state={props.navigationState}
              jumpTo={props.jumpTo}
            />
          )}
          sceneContainerStyle={styles.tabScene}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  flatItem: {
    alignItems: "center",
    justifyContent: "center",
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundColor,
    padding: 0,
    height: "100%",
    width: "100%",
    alignItems: "center",
  },
  programItem: {
    borderRadius: 10, // Add rounded corners
    borderWidth: 1, // Add border,
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    margin: 4,
    width: Dimensions.get('screen').width/2.5,
    borderColor:"rgba(0,0,0,0.1)"
  },
  gradient: {
    borderRadius: 10,
    borderWidth: 1, // Add border,
  
  },
  logo: {
    width: "100%", // Take the full width of the container
    height: 90,
    aspectRatio: 16 / 9,
    alignItems: "center",
    alignContent: "center",
  
  
  },
  programTitle: {
    fontSize: 18,
    fontFamily:FONT.bold,
    marginTop: 8,
    color: "#fff",
  },
  programDescription: {
    fontSize: 14,
    marginTop: 8,
    color: "#fff",
  },
  tabBar: {
    flexDirection: "row",
    height: 40,
    backgroundColor: COLORS.backgroundColor,
    fontSize: 12,
    padding: 5,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  tabItem: {
    borderWidth: 1,
    borderColor: COLORS.textColor,
  },
  tabScene: {
    paddingVertical: 15,
    flex: 1,
    height: "100%",
    backgroundColor: COLORS.backgroundColor,
    paddingBottom: 0,
  },
  tabIndicator: {
    backgroundColor: COLORS.primaryHlColor, // You can set your desired color for the tab indicator
  },
  tabLabel: {
    color: COLORS.textColor, // You can set your desired color for tab labels
  },
});

export default ProgramList;
