import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { getAccessToken, getContest } from "../../api/auth";
import { COLORS, FONT } from "../../constants";
import { useContest } from "../../context/contests";
import { router } from "expo-router";


const ContestList = () => {
  const [accessToken, setAccessToken] = useState(null); // Stor
  const [contest, setContest] = useState([]);
  const [loading, setLoading] = useState(true);
  const windowWidth = Dimensions.get("window").width;
  const [orientation, setOrientation] = useState("");
  const [numColumns, setnumColumns] = useState(2);
  const { toggleContest } = useContest();

  useEffect(() => {
    const YOUR_API_KEY =
      "QU03SzE4N3BjZUNNZW1ONXVHeXREblhNWXIyem9oVmpRUnpERkFtdDp5a0VDUE5MRVNPVXlrbURMUVFKRHZRdkJZYmlGTXlOWWsxQ3ZLMTh5";
    getOrientation();
    getAccessToken(YOUR_API_KEY)
      .then((token) => {
        setAccessToken(token);
        // Use the access token to fetch data from the API
        
    getContest(token).then((response) => {
      const data = response;
      setContest(data);
      setLoading(false);
    })
    .catch((error) => {
      console.error("Error fetching data:", error);
      setLoading(false);
    
    });

      })
      .catch((error) => {
        console.error("Error obtaining access token:", error);
        setLoading(false);
      });
  }, []);

  const getOrientation = () => {
    if (Dimensions.get("window").height < Dimensions.get("window").width) {
      if (Dimensions.get("window").width >= 1200) {
        setOrientation("LANDSCAPE_ALT");
        setnumColumns(6);
      } else if (Dimensions.get("window").width >= 1100) {
        setOrientation("LANDSCAPE_LOW_ALT");
        setnumColumns(5);
      } else {
        setOrientation("LANDSCAPE");
        setnumColumns(4);
      }
    } else {
      setOrientation("PORTRAIT");
      setnumColumns(2);
    }
    return orientation;
  };

  const renderContestItem = ({ item }) => {
    const handlePress = ( item) => {
      toggleContest(item);

      router.push("/contest/" + item.post_title);
    };
    return (
      <TouchableOpacity onPress={()=>{handlePress(item)}}>
        <View style={styles.contestItem}>
          <View style={styles.thumbnailContainer}>
            <Image source={{ uri: item.image }} style={styles.thumbnail} resizeMode='cover' />
          </View>
          <Text style={styles.title} numberOfLines={3} ellipsizeMode="tail">
            {item.post_title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.textColor} />
        </View>
      ) : (
        <FlatList
          key={orientation}
          data={contest}
          renderItem={renderContestItem}
          keyExtractor={(item) => item.ID.toString()}
          numColumns={numColumns}
        ></FlatList>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  contestItem: {
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    margin: 4, // Add margin to separate items
    borderRadius: 4, // Add rounded corners
    borderWidth: 1, // Add border,
    borderColor: "rgba(0,0,0,0.20)",

    width: Dimensions.get('screen').width/2.2,
  },
  container: {
    flex: 1,
    padding: 5,
    alignItems: "center",
    backgroundColor: COLORS.backgroundColor,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  thumbnailContainer: {
    position: "relative", // Create a positioning context
    width: "100%",
    aspectRatio: 16 / 9, // Set a 16:9 aspect ratio for the thumbnail container
  },
  thumbnail: {
    width: "100%",
    height: "100%", // Ensure the thumbnail fills the container
    // Add rounded corners  borderRadius: 4
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },

  title: {
    fontSize: 14,
    margin: 2, // Spacing between thumbnail and title
    color: COLORS.textColor,
    textAlign: "left",
    fontFamily:FONT.medium
  },
});

export default ContestList;
