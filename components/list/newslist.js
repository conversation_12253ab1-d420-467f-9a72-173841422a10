import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import axios from "axios";
import { TabView } from "react-native-tab-view";
import { getAccessToken } from "../../api/auth";
import { COLORS, TEXTDATA, NUMBERS, FONT } from "../../constants";
import { useRouter } from "expo-router";
import { useNews } from "../../context/news";
import AsyncStorage from "@react-native-async-storage/async-storage";

const customStyle = StyleSheet.flatten([
  { borderWidth: 1, borderRadius: 10 },
  StyleSheet.absoluteFillObject,
]);
const router = useRouter();


const NewsList = () => {
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [index, setIndex] = useState(0);
  const [numColumns, setnumColumns] = useState(2);
  const [orientation, setOrientation] = useState("");
  const {toggleNews} = useNews();
  const [routes] = useState([
    { key: "festival", title: TEXTDATA.newsFirstBarName },
    { key: "events", title: TEXTDATA.newsSecondBarName },
  
  ]);
  const [accessToken, setAccessToken] = useState(null); // Stor

  useEffect(() => {
    // Fetch the access token first
   
    getOrientation();
    fetchEventsData();
  }, []);


  const fetchEventsData = async () => {

    const YOUR_API_KEY =
    "QU03SzE4N3BjZUNNZW1ONXVHeXREblhNWXIyem9oVmpRUnpERkFtdDp5a0VDUE5MRVNPVXlrbURMUVFKRHZRdkJZYmlGTXlOWWsxQ3ZLMTh5"; // Replace with your actual API key
    try {
      const cachedData = await AsyncStorage.getItem("eventsData");
      const cachedTimestamp = await AsyncStorage.getItem("eventsDataTimestamp");
  
      if (cachedData && cachedTimestamp) {
        const currentTime = new Date().getTime();
        const cachedTime = parseInt(cachedTimestamp, 10);
        const expirationTime = 30 * 60 * 1000; // 12 hours in milliseconds
  
        if (currentTime - cachedTime < expirationTime) {
          // Data is still valid, use it
          const cachedEvents = JSON.parse(cachedData);
          setNews(cachedEvents);
          setLoading(false);
          return;
        }
      }
  
      // Fetch new data if cache is expired or doesn't exist
      const token = await getAccessToken(YOUR_API_KEY);
      setAccessToken(token);
  
      const response = await axios.get("https://alrayyan.tv/wp-json/tvapi/v2/listevents", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
  
      const data = response.data;
  
      // Update the cache with the fetched data
      await AsyncStorage.setItem("eventsData", JSON.stringify(data));
      await AsyncStorage.setItem("eventsDataTimestamp", String(new Date().getTime()));
  
      setNews(data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching events data:", error);
      setLoading(false);
    }
  };
  
  const getOrientation = () => {
    if (Dimensions.get("window").height < Dimensions.get("window").width) {
      if (Dimensions.get("window").width >= 1200) {
        setOrientation("LANDSCAPE_ALT");
        setnumColumns(6);
      } else if (Dimensions.get("window").width >= 1100) {
        setOrientation("LANDSCAPE_LOW_ALT");
        setnumColumns(5);
      } else {
        setOrientation("LANDSCAPE");
        setnumColumns(4);
      }
    } else {
      setOrientation("PORTRAIT");
      setnumColumns(2);
    }
    return orientation;
  };
  const TabBarComponent = ({ state, jumpTo }) => {
    return (
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const isFocused = state.index === index;
          const tabStyle = {
            backgroundColor: isFocused
              ? COLORS.primaryHlColor
              : COLORS.ttabBackgroundColor, // Change the background color
            padding: 3,
            width: Dimensions.get('screen').width/2.2,
            height: 40,
            alignItems: "center",
            justifyContent: "center",
            borderRadius: 0, // Set your preferred border radius
            borderWidth: 1,
            borderColor: COLORS.textColor,
            fontSize: 12,
          };

          return (
            <TouchableWithoutFeedback
              key={route.key}
              onPress={() => {
                jumpTo(route.key);
              }}
            >
              <View style={tabStyle}>
                <Text
                  style={{
                    color: isFocused ? COLORS.textColor : COLORS.textColor,
                    fontSize: 13,
                  }}
                >
                  {route.title}
                </Text>
              </View>
            </TouchableWithoutFeedback>
          );
        })}
      </View>
    );
  };

  const renderNewsItem = ({ item }) => {

    const displayShow = (item) => {

      toggleNews(item);
      router.push("/news/" + item.post_title);
    };
    return (
      <TouchableOpacity
        key={item.ID}
        onPress={() => {
          displayShow(item);
        }}
      >
        <View style={styles.newsItem}>
         
          <Image
            source={{ uri: item.attachment }}
            style={styles.logo}
            alt={item.post_title}
          />
            <Text style={styles.newsTitle} numberOfLines={5} ellipsizeMode="tail">{item.post_title}</Text>
        </View>
      </TouchableOpacity>
    );
  
  };
  

  const renderScene = ({ route }) => {
    switch (route.key) {
      case "festival":
        return (
          <FlatList
            key={orientation}
            data={news.filter(
              (item) =>
                item.cat_name === "مهرجانات"
            )}
            renderItem={renderNewsItem}
            keyExtractor={(item) => item.ID.toString()}
            numColumns={numColumns}
        
          />
        );
      case "events":
        return (
          <FlatList
            key={orientation}
            data={news.filter(
              (item) => item.cat_name === "فعاليات"
            )}
            renderItem={renderNewsItem}
            keyExtractor={(item) => item.ID.toString()}
            numColumns={numColumns}
           
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.textColor} />
        </View>
      ) : (
        <TabView
       
          navigationState={{ index, routes }}
          renderScene={renderScene}
          onIndexChange={setIndex}
          renderTabBar={(props) => (
            <TabBarComponent
              navigation={props.navigation}
              state={props.navigationState}
              jumpTo={props.jumpTo}
            />
          )}
          sceneContainerStyle={styles.tabScene}
          style={{width:"100%"}}
         
        
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  flatItem: {
    alignItems: "center",
    justifyContent: "center",
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundColor,
    padding: 0,
    width: "100%",
    alignItems: "center",
    
  },
  newsItem: {
    borderRadius: 10, // Add rounded corners
    borderWidth: 1, // Add border,
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    margin: 4,
    width:Dimensions.get('screen').width/2.2,
    
    borderColor:"rgba(0,0,0,0.1)"
  },
  gradient: {
    borderRadius: 10,
    borderWidth: 1, // Add border,
  
  },
  logo: {
    width: '100%',
    height: (Dimensions.get('screen').width/2.2*9)/16,
    alignItems: "center",
    alignContent: "center",
  
  
  },
  newsTitle: {
    fontSize: 16,
    color: "#fff",
    marginTop: 8,
    textAlign:"center",
    padding:5,
    fontFamily:FONT.medium
  },
  programDescription: {
    fontSize: 14,
    marginTop: 8,
    color: "#fff",
    fontFamily:FONT.light
  },
  tabBar: {
    flexDirection: "row",
    height: 40,
    backgroundColor: COLORS.backgroundColor,
    fontSize: 12,
    padding: 5,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  tabItem: {
    borderWidth: 1,
    borderColor: COLORS.textColor,
  },
  tabScene: {
    paddingVertical: 15,
    flex: 1,
    alignItems:"center",
    justifyContent:"center",

    backgroundColor: COLORS.backgroundColor,
    paddingBottom: 0,
  },
  tabIndicator: {
    backgroundColor: COLORS.primaryHlColor, // You can set your desired color for the tab indicator
  },
  tabLabel: {
    color: COLORS.textColor, // You can set your desired color for tab labels
  },
});

export default NewsList;
