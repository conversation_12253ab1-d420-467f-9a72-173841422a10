{"expo": {"scheme": "acme", "web": {"bundler": "metro"}, "plugins": ["expo-router", "sentry-expo", "expo-localization", "@react-native-firebase/app", "@react-native-firebase/perf", "@react-native-firebase/crashlytics", ["expo-build-properties", {"ios": {"useFrameworks": "static"}}]], "name": "Al Rayyan TV", "slug": "<PERSON><PERSON><PERSON>", "orientation": "default", "version": "3.3.21", "icon": "./assets/splash/alrayyan_icon.png", "splash": {"image": "./assets/splash/download.png", "resizeMode": "cover", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "requireFullScreen": true, "bundleIdentifier": "com.rayyanmedia.rayyantv", "buildNumber": "3.3.21", "jsEngine": "jsc", "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"UIFileSharingEnabled": true, "LSSupportsOpeningDocumentsInPlace": true, "UIStatusBarStyle": "light-content", "UIRequiresFullScreen": true, "NSMicrophoneUsageDescription": "We need access to the microphone to record audio for video creation."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/splash/alrayyan_icon.png"}, "googleServicesFile": "./google-services.json", "versionCode": 30322, "permissions": ["READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"], "package": "com.LinkDev.Alrayyan.App"}, "extra": {"router": {"origin": false}, "eas": {"projectId": "aac57e9a-8155-4988-904f-40f828b40207"}, "supportsRTL": true}}}