import React, { useState, useEffect } from "react";
import * as SplashScreen from "expo-splash-screen";
import SplashPlayer from "../components/custom/splash";

function useSplashScreen ()
{

    const [splash, setSplash] = useState(true);
    const toggleSplash = () => {
        setSplash(false);
      }

    useEffect(()=>
    {

        SplashScreen.hideAsync();

    },[])

    if (splash ) {
        return (<SplashPlayer toggleSplash={toggleSplash} />);
      }
}

export {useSplashScreen}