PODS:
  - boost (1.76.0)
  - DoubleConversion (1.1.6)
  - EXApplication (5.3.1):
    - ExpoModulesCore
  - EXAV (13.4.1):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXConstants (14.4.2):
    - ExpoModulesCore
  - EXFileSystem (15.4.4):
    - ExpoModulesCore
  - EXFont (11.4.0):
    - ExpoModulesCore
  - EXJSONUtils (0.7.1)
  - EXManifests (0.7.2):
    - ExpoModulesCore
  - Expo (49.0.13):
    - ExpoModulesCore
  - expo-dev-client (2.4.13):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (2.4.15):
    - EXManifests
    - expo-dev-launcher/Main (= 2.4.15)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-launcher/Main (2.4.15):
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-launcher/Unsafe (2.4.15):
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-menu (3.2.4):
    - expo-dev-menu/Main (= 3.2.4)
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu-interface (1.3.0)
  - expo-dev-menu/Main (3.2.4):
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu/SafeAreaView (3.2.4):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu/Vendored (3.2.4):
    - expo-dev-menu/SafeAreaView
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - ExpoDevice (5.4.0):
    - ExpoModulesCore
  - ExpoDocumentPicker (11.5.4):
    - ExpoModulesCore
  - ExpoHead (0.0.11):
    - ExpoModulesCore
  - ExpoImage (1.3.5):
    - ExpoModulesCore
    - SDWebImage (~> 5.15.8)
    - SDWebImageAVIFCoder (~> 0.10.0)
    - SDWebImageSVGCoder (~> 1.7.0)
    - SDWebImageWebPCoder (~> 0.11.0)
  - ExpoKeepAwake (12.3.0):
    - ExpoModulesCore
  - ExpoLinearGradient (12.3.0):
    - ExpoModulesCore
  - ExpoLocalization (14.3.0):
    - ExpoModulesCore
  - ExpoMailComposer (12.3.0):
    - ExpoModulesCore
  - ExpoModulesCore (1.5.11):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - ReactCommon/turbomodule/core
  - ExpoScreenOrientation (6.0.6):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - EXSplashScreen (0.20.5):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - EXUpdatesInterface (0.10.1)
  - FBLazyVector (0.72.10)
  - FBReactNativeSpec (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.10)
    - RCTTypeSafety (= 0.72.10)
    - React-Core (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - Firebase/CoreOnly (10.19.0):
    - FirebaseCore (= 10.19.0)
  - Firebase/Crashlytics (10.19.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.19.0)
  - Firebase/Messaging (10.19.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.19.0)
  - Firebase/Performance (10.19.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 10.19.0)
  - FirebaseABTesting (10.22.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCore (10.19.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.19.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.21.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.19.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.21.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.19.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebasePerformance (10.19.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfig (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/ISASwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.22.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseSessions (10.22.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.10)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.22.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleDataTransport (9.3.0):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.12.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.12.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.12.0)
  - GoogleUtilities/Logger (7.12.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.12.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.12.0)"
  - GoogleUtilities/Reachability (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.12.0):
    - GoogleUtilities/Logger
  - libaom (3.0.0):
    - libvmaf (>= 2.2.0)
  - libavif (0.11.1):
    - libavif/libaom (= 0.11.1)
  - libavif/core (0.11.1)
  - libavif/libaom (0.11.1):
    - libaom (>= 2.0.0)
    - libavif/core
  - libvmaf (2.3.1)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - PromisesObjC (2.3.1)
  - PromisesSwift (2.3.1):
    - PromisesObjC (= 2.3.1)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.72.10)
  - RCTTypeSafety (0.72.10):
    - FBLazyVector (= 0.72.10)
    - RCTRequired (= 0.72.10)
    - React-Core (= 0.72.10)
  - RCTYouTube (2.0.2):
    - React
    - YoutubePlayer-in-WKWebView (~> 0.3.1)
  - React (0.72.10):
    - React-Core (= 0.72.10)
    - React-Core/DevSupport (= 0.72.10)
    - React-Core/RCTWebSocket (= 0.72.10)
    - React-RCTActionSheet (= 0.72.10)
    - React-RCTAnimation (= 0.72.10)
    - React-RCTBlob (= 0.72.10)
    - React-RCTImage (= 0.72.10)
    - React-RCTLinking (= 0.72.10)
    - React-RCTNetwork (= 0.72.10)
    - React-RCTSettings (= 0.72.10)
    - React-RCTText (= 0.72.10)
    - React-RCTVibration (= 0.72.10)
  - React-callinvoker (0.72.10)
  - React-Codegen (0.72.10):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.10)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.10)
    - React-Core/RCTWebSocket (= 0.72.10)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.10)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.10)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/CoreModulesHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-RCTBlob
    - React-RCTImage (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.10):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.10)
    - React-debug (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-jsinspector (= 0.72.10)
    - React-logger (= 0.72.10)
    - React-perflogger (= 0.72.10)
    - React-runtimeexecutor (= 0.72.10)
  - React-debug (0.72.10)
  - React-jsc (0.72.10):
    - React-jsc/Fabric (= 0.72.10)
    - React-jsi (= 0.72.10)
  - React-jsc/Fabric (0.72.10):
    - React-jsi (= 0.72.10)
  - React-jsi (0.72.10):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.10):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-perflogger (= 0.72.10)
  - React-jsinspector (0.72.10)
  - React-logger (0.72.10):
    - glog
  - react-native-orientation-locker (1.6.0):
    - React-Core
  - react-native-pager-view (6.2.0):
    - React-Core
  - react-native-restart (0.0.27):
    - React-Core
  - react-native-safe-area-context (4.6.3):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - react-native-slider (4.4.2):
    - React-Core
  - react-native-webview (13.2.2):
    - React-Core
  - React-NativeModulesApple (0.72.10):
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.10)
  - React-RCTActionSheet (0.72.10):
    - React-Core/RCTActionSheetHeaders (= 0.72.10)
  - React-RCTAnimation (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTAnimationHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTAppDelegate (0.72.10):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-jsc
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTBlobHeaders (= 0.72.10)
    - React-Core/RCTWebSocket (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-RCTNetwork (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTImage (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTImageHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-RCTNetwork (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTLinking (0.72.10):
    - React-Codegen (= 0.72.10)
    - React-Core/RCTLinkingHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTNetwork (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTNetworkHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTSettings (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTSettingsHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTText (0.72.10):
    - React-Core/RCTTextHeaders (= 0.72.10)
  - React-RCTVibration (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTVibrationHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-rncore (0.72.10)
  - React-runtimeexecutor (0.72.10):
    - React-jsi (= 0.72.10)
  - React-runtimescheduler (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.10):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.10)
    - React-cxxreact (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-logger (= 0.72.10)
    - React-perflogger (= 0.72.10)
  - ReactCommon/turbomodule/core (0.72.10):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.10)
    - React-cxxreact (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-logger (= 0.72.10)
    - React-perflogger (= 0.72.10)
  - RNCAsyncStorage (1.18.2):
    - React-Core
  - RNFBApp (18.7.2):
    - Firebase/CoreOnly (= 10.19.0)
    - React-Core
  - RNFBCrashlytics (18.7.2):
    - Firebase/Crashlytics (= 10.19.0)
    - FirebaseCoreExtension (= 10.19.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (18.7.2):
    - Firebase/Messaging (= 10.19.0)
    - FirebaseCoreExtension (= 10.19.0)
    - React-Core
    - RNFBApp
  - RNFBPerf (18.7.2):
    - Firebase/Performance (= 10.19.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.12.1):
    - React-Core
  - RNReanimated (3.3.0):
    - DoubleConversion
    - FBLazyVector
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTAppDelegate
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.22.1):
    - React-Core
    - React-RCTImage
  - RNSentry (5.10.0):
    - React-Core
    - Sentry/HybridSDK (= 8.11.0)
  - SDWebImage (5.15.8):
    - SDWebImage/Core (= 5.15.8)
  - SDWebImage/Core (5.15.8)
  - SDWebImageAVIFCoder (0.10.1):
    - libavif (>= 0.11.0)
    - SDWebImage (~> 5.10)
  - SDWebImageSVGCoder (1.7.0):
    - SDWebImage/Core (~> 5.6)
  - SDWebImageWebPCoder (0.11.0):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.15)
  - Sentry/HybridSDK (8.11.0):
    - SentryPrivate (= 8.11.0)
  - SentryPrivate (8.11.0)
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)
  - YoutubePlayer-in-WKWebView (0.3.8)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXApplication (from `../node_modules/expo-application/ios`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXFileSystem (from `../node_modules/expo-file-system/ios`)
  - EXFont (from `../node_modules/expo-font/ios`)
  - EXJSONUtils (from `../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../node_modules/expo-manifests/ios`)
  - Expo (from `../node_modules/expo`)
  - expo-dev-client (from `../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../node_modules/expo-dev-menu-interface/ios`)
  - ExpoDevice (from `../node_modules/expo-device/ios`)
  - ExpoDocumentPicker (from `../node_modules/expo-document-picker/ios`)
  - ExpoHead (from `../node_modules/expo-head/ios`)
  - ExpoImage (from `../node_modules/expo-image/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../node_modules/expo-linear-gradient/ios`)
  - ExpoLocalization (from `../node_modules/expo-localization/ios`)
  - ExpoMailComposer (from `../node_modules/expo-mail-composer/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoScreenOrientation (from `../node_modules/expo-screen-orientation/ios`)
  - EXSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - EXUpdatesInterface (from `../node_modules/expo-updates-interface/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - RCTYouTube (from `../node_modules/react-native-youtube`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-orientation-locker (from `../node_modules/react-native-orientation-locker`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-restart (from `../node_modules/react-native-restart`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - "RNFBPerf (from `../node_modules/@react-native-firebase/perf`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseABTesting
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseSessions
    - FirebaseSharedSwift
    - fmt
    - GoogleDataTransport
    - GoogleUtilities
    - libaom
    - libavif
    - libvmaf
    - libwebp
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageAVIFCoder
    - SDWebImageSVGCoder
    - SDWebImageWebPCoder
    - Sentry
    - SentryPrivate
    - SocketRocket
    - YoutubePlayer-in-WKWebView

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXApplication:
    :path: "../node_modules/expo-application/ios"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  EXFont:
    :path: "../node_modules/expo-font/ios"
  EXJSONUtils:
    :path: "../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../node_modules/expo-manifests/ios"
  Expo:
    :path: "../node_modules/expo"
  expo-dev-client:
    :path: "../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../node_modules/expo-dev-menu-interface/ios"
  ExpoDevice:
    :path: "../node_modules/expo-device/ios"
  ExpoDocumentPicker:
    :path: "../node_modules/expo-document-picker/ios"
  ExpoHead:
    :path: "../node_modules/expo-head/ios"
  ExpoImage:
    :path: "../node_modules/expo-image/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../node_modules/expo-linear-gradient/ios"
  ExpoLocalization:
    :path: "../node_modules/expo-localization/ios"
  ExpoMailComposer:
    :path: "../node_modules/expo-mail-composer/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoScreenOrientation:
    :path: "../node_modules/expo-screen-orientation/ios"
  EXSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  EXUpdatesInterface:
    :path: "../node_modules/expo-updates-interface/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  RCTYouTube:
    :path: "../node_modules/react-native-youtube"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-orientation-locker:
    :path: "../node_modules/react-native-orientation-locker"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-restart:
    :path: "../node_modules/react-native-restart"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFBPerf:
    :path: "../node_modules/@react-native-firebase/perf"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  EXApplication: 042aa2e3f05258a16962ea1a9914bf288db9c9a1
  EXAV: f393dfc0b28214d62855a31e06eb21d426d6e2da
  EXConstants: ce5bbea779da8031ac818c36bea41b10e14d04e1
  EXFileSystem: 2b826a3bf1071a4b80a8457e97124783d1ac860e
  EXFont: 738c44c390953ebcbab075a4848bfbef025fd9ee
  EXJSONUtils: 6802be4282d42b97c51682468ddc1026a06f8276
  EXManifests: cf66451b11b2c2f6464917528d792759f7fd6ce0
  Expo: e7d2116b947e2e6fdeb09ee4f2754f819426d1b6
  expo-dev-client: f350e6ddced33d2c14008c6d70c7904c0cd78b1f
  expo-dev-launcher: 6c98be926e385f889c048b708a4133111064e492
  expo-dev-menu: bc72445908a9cbac6d280be4f253bed71c193aa4
  expo-dev-menu-interface: bda969497e73dadc2663c479e0fa726ca79a306e
  ExpoDevice: 1c1b0c9cad96c292c1de73948649cfd654b2b3c0
  ExpoDocumentPicker: 5cb7389ff935b4addefdd466a606de51a512e922
  ExpoHead: 8da645502363b53eeb99d99276a0adbe0ec8cff0
  ExpoImage: 723efcb8d9377ae8645a6f7a1579305f5b80a76f
  ExpoKeepAwake: be4cbd52d9b177cde0fd66daa1913afa3161fc1d
  ExpoLinearGradient: 5966dd5d49872cc9c104fedc8bbc298b6049b2e8
  ExpoLocalization: be37fdd0b5930c6a49cd307b4542f4b426d6134c
  ExpoMailComposer: feaebd62095efad901ede864b95406f02bf61681
  ExpoModulesCore: 81037d28453615c4206a6a2ca7556a0f364a0a0a
  ExpoScreenOrientation: 3e3b9a3a8ec8498d51992a0a819deda9d82cbaac
  EXSplashScreen: 2c80621e4bc952f5bea328304c2436f5fe21c2a0
  EXUpdatesInterface: 82ed48d417cdcd376c12ca1c2ce390d35500bed6
  FBLazyVector: f91d538f197fa71a7d5b77ec2069d49550c0eb96
  FBReactNativeSpec: b13d1c23d6ed82d6b66aad7a253edf8ba76c4a4c
  Firebase: 63ce8ece0d43743dc28eacac0c6867a2d7fd5a9d
  FirebaseABTesting: 66d2594b36d4ff6e7d3c8719802100990de05857
  FirebaseCore: dc5c7badf99d47613c52b2e3a57a64cd187f8554
  FirebaseCoreExtension: c08d14c7b22e07994e876d837e6f58642f340087
  FirebaseCoreInternal: 43c1788eaeee9d1b97caaa751af567ce11010d00
  FirebaseCrashlytics: a4d2ad12f5c07ec8ee0ebc89133a45498a293ba6
  FirebaseInstallations: 390ea1d10a4d02b20c965cbfd527ee9b3b412acb
  FirebaseMessaging: d322bd030a0e97398bf134746e087b88e6ba0074
  FirebasePerformance: 634a9b06effc436c991273d90053a2086dd2d427
  FirebaseRemoteConfig: e1b992a94d3674dddbcaf5d0d31a0312156ceb1c
  FirebaseSessions: cd97fb07674f3906619c871eefbd260a1546c9d3
  FirebaseSharedSwift: 48076404e6e52372290d15a07d2ed1d2f1754023
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleDataTransport: 57c22343ab29bc686febbf7cbb13bad167c2d8fe
  GoogleUtilities: 0759d1a57ebb953965c2dfe0ba4c82e95ccc2e34
  libaom: 144606b1da4b5915a1054383c3a4459ccdb3c661
  libavif: 84bbb62fb232c3018d6f1bab79beea87e35de7b7
  libvmaf: 27f523f1e63c694d14d534cd0fddd2fab0ae8711
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  PromisesSwift: 28dca69a9c40779916ac2d6985a0192a5cb4a265
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: b4d3068afa6f52ec5260a8417053b1f1b421483d
  RCTTypeSafety: a4551b3d338c96435f63bf06d564055c1d3cc0ac
  RCTYouTube: a8bb45705622a6fc9decf64be04128d3658ed411
  React: 66caa2a8192a35d7ba466a5fdf5dc06ee4a5f6dd
  React-callinvoker: e5b55e46894c2dd1bcdc19d4f82b0f7f631d1237
  React-Codegen: 6a1ecd7c2a5ca046b0d5c58a73819227b2d66984
  React-Core: a8cb28a8655a0d80fa340b8d79320b438af1a675
  React-CoreModules: 965a80829f789cb6a99308e99352369809f01690
  React-cxxreact: aa2a58d826b08afb9cf062e0f14f51aa606dd133
  React-debug: c61caaf4d8d74bad779deed1d94faf27b823f290
  React-jsc: 663d1bb8400d751370f39b639e2e5f58fdebc2e2
  React-jsi: c59ecc480de0d02348b4625c86e9ff947eaf9cd8
  React-jsiexecutor: 542a507e6d3985fda431c614fd456aebf59bdc66
  React-jsinspector: de0198127395fec3058140a20c045167f761bb16
  React-logger: dc3a2b174d79c2da635059212747d8d929b54e06
  react-native-orientation-locker: 4409c5b12b65f942e75449872b4f078b6f27af81
  react-native-pager-view: 0ccb8bf60e2ebd38b1f3669fa3650ecce81db2df
  react-native-restart: 7595693413fe3ca15893702f2c8306c62a708162
  react-native-safe-area-context: 36cc67648134e89465663b8172336a19eeda493d
  react-native-slider: 33b8d190b59d4f67a541061bb91775d53d617d9d
  react-native-webview: b8ec89966713985111a14d6e4bf98d8b54bced0d
  React-NativeModulesApple: 8bd058bdbbd70be0024043d1d0846085031fb396
  React-perflogger: 43287389ea08993c300897a46f95cfac04bb6c1a
  React-RCTActionSheet: 923afe77f9bb89da7c1f98e2730bfc9dde0eed6d
  React-RCTAnimation: b88e8c2c7911eef712d512aed65d1f073724c121
  React-RCTAppDelegate: 114b7cabad129bab1ce66256798dbe9936237e73
  React-RCTBlob: 8dfe1412e5983ef178da8355a5f97e0b4273979f
  React-RCTImage: 9b5e844dc14086707a55d92f72b997635be8f39a
  React-RCTLinking: 2fd7420b1266f51a1dc73be0c92dab656d7c7fb4
  React-RCTNetwork: 16b60cd813d69bb1cd28429bdcdc544f9f69d713
  React-RCTSettings: 7070fe45328477283e93a883bb0df0d008aee2a3
  React-RCTText: 7adddb518ac362b2398fedf0c64105e0dab29441
  React-RCTVibration: ac1f64d2459fe335b2fe431eb033830326864d55
  React-rncore: 9414d658e6bbcee9d9d05836e1c766b0296fb9f5
  React-runtimeexecutor: 2b2c09edbca4c9a667428e8c93959f66b3b53140
  React-runtimescheduler: 991a4a1e940b08e88df9adb142276044a062ee2f
  React-utils: a715392dfaaa383668566e400f40493093c08d7e
  ReactCommon: 710084eca7fe73374213694c767c3dff61f3319c
  RNCAsyncStorage: ddc4ee162bfd41b0d2c68bf2d95acd81dd7f1f93
  RNFBApp: 65edc8bc114a6e3fc004666be89fd7ccf4c2d923
  RNFBCrashlytics: 5323b1b36915e36e1e7bd00294b1efd34d93326c
  RNFBMessaging: 9d734bb8f1f19b3fbd0549f9ab2895b45b43203e
  RNFBPerf: 0708e00c0944048eda4ba355148e00b6acdcadc8
  RNGestureHandler: c0d04458598fcb26052494ae23dda8f8f5162b13
  RNReanimated: 3ffa3d63576ecd26a4f8772d03029cccaf41b5d7
  RNScreens: 50ffe2fa2342eabb2d0afbe19f7c1af286bc7fb3
  RNSentry: 11917f7bf3e28806aca4c2791c6ba7522d8f09fe
  SDWebImage: cb032eba469c54e0000e78bcb0a13cdde0a52798
  SDWebImageAVIFCoder: 8348fef6d0ec69e129c66c9fe4d74fbfbf366112
  SDWebImageSVGCoder: 15a300a97ec1c8ac958f009c02220ac0402e936c
  SDWebImageWebPCoder: 295a6573c512f54ad2dd58098e64e17dcf008499
  Sentry: 39d57e691e311bdb73bc1ab5bbebbd6bc890050d
  SentryPrivate: 48712023cdfd523735c2edb6b06bedf26c4730a3
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: d0003f849d2b5224c072cef6568b540d8bb15cd3
  YoutubePlayer-in-WKWebView: 4fca3b4f6f09940077bfbae7bddb771f2b43aacd

PODFILE CHECKSUM: e1228f6ff6691ad42827ebfc9e7d3493b402b5eb

COCOAPODS: 1.15.2
