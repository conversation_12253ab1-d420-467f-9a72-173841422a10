<<<<<<< HEAD
# Expo Router Example

Use [`expo-router`](https://expo.github.io/router) to build native navigation using files in the `app/` directory.

## 🚀 How to use

```sh
npx create-expo-app -e with-router
```

## 📝 Notes

- [Expo Router: Docs](https://expo.github.io/router)
- [Expo Router: Repo](https://github.com/expo/router)
=======
# Al Rayyan TV app

Al Rayyan TV hybrid mobile app.
Application is built using Expo & React Native.

## Setup instructions

### 1. Install dependencies

```
mkdir al-rayyan-tv
cd al-rayyan-tv
git clone https://git.citrusinformatics.com/RGB/al-rayyan/al-rayyan-tv.git
cd al-rayyan-tv 
npm install
```

### 2. Run the app 

Setup for iOS
```
To prebuild expo app to obtain ios folder in project :
   
   npx expo prebuild -p ios
```

Setup Pod 
```
   cd ios
   pod install
```

Run in iOS device
```
   npx expo start
   select IOS Simulator
```

Run in Android
```
npx expo start
select Android Simulator
```
>>>>>>> dev
