import { createContext,useContext,useState } from "react";

// For Shows Page
const HomeContext = createContext();

function useHome() {
    return useContext(HomeContext);
  }
  

  function HomeProvider({ children }) {
    const [home, setHome] = useState(null); // Default Show is null
  
    const toggleHome = (item) => {
      setHome(item);
    };
  
    return (
      <HomeContext.Provider value={{ home, toggleHome }}>
        {children}
      </HomeContext.Provider>
    );
  }

export {HomeContext,useHome,HomeProvider};
