import { createContext,useContext,useState } from "react";

// For Shows Page
const AlbumContext = createContext();

function useAlbum() {
    return useContext(AlbumContext);
  }
  

  function AlbumProvider({ children }) {
    const [image, setImages] = useState(null); // Default Show is null
  
    const toggleAlbum = (item) => {
      setImages(item);
    };
  
    return (
      <AlbumContext.Provider value={{ image, toggleAlbum }}>
        {children}
      </AlbumContext.Provider>
    );
  }

export {AlbumContext,useAlbum,AlbumProvider};
