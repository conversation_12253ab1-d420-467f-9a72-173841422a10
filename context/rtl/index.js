import React, { createContext, useContext, useReducer } from 'react';
import AsyncStorage from "@react-native-async-storage/async-storage";


const LayoutContext = createContext();

export const useLayout = () => {
  return useContext(LayoutContext);
};

async function getDirection()
{
    const dir = await AsyncStorage.getItem("dir");
    if (dir)
    {
        return dir;

    }
    else
    {
        return "ltr";

    }
}

const initialState = {
  layout: getDirection(),
};

const layoutReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LAYOUT_DIRECTION':
      return { ...state, layout: action.direction };
    default:
      return state;
  }
};

export const LayoutProvider = ({ children }) => {
  const [state, dispatch] = useReducer(layoutReducer, initialState);

  return (
    <LayoutContext.Provider value={{ state, dispatch }}>
      {children}
    </LayoutContext.Provider>
  );
};