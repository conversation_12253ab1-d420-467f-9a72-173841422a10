import { createContext,useContext,useState } from "react";

// For Shows Page
const ShowContext = createContext();

function useShow() {
    return useContext(ShowContext);
  }
  

  function ShowProvider({ children }) {
    const [show, setShow] = useState(null); // Default Show is null
  
    const toggleShow = (item) => {
      setShow(item);
    };
  
    return (
      <ShowContext.Provider value={{ show, toggleShow }}>
        {children}
      </ShowContext.Provider>
    );
  }

export {ShowContext,useShow,ShowProvider};
