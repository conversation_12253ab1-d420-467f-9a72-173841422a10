import { createContext,useContext,useState } from "react";

// For newss Page
const NewsContext = createContext();

function useNews() {
    return useContext(NewsContext);
  }
  

  function NewsProvider({ children }) {
    const [news, setnews] = useState(null); // Default news is null
  
    const toggleNews = (item) => {
      setnews(item);
    };
  
    return (
      <NewsContext.Provider value={{ news, toggleNews }}>
        {children}
      </NewsContext.Provider>
    );
  }

export {NewsContext,useNews,NewsProvider};
