import { createContext,useContext,useState } from "react";

// For Shows Page
const ContestContext = createContext();

function useContest() {
    return useContext(ContestContext);
  }
  

  function ContestProvider({ children }) {
    const [contest, setContest] = useState(null); // Default Show is null
  
    const toggleContest = (item) => {
      setContest(item);
    };
  
    return (
      <ContestContext.Provider value={{ contest, toggleContest }}>
        {children}
      </ContestContext.Provider>
    );
  }

export {ContestContext,useContest,ContestProvider};
