import { Stack } from "expo-router";
import { COLORS, FONT, TEXTDATA } from "../../../../constants";
import SecondHeader from "../../../../components/header/secondHeader";
import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  Image,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
  Modal,
  PanResponder,
  Platform,
  Linking,
} from "react-native";
import axios from "axios";
import { FontAwesome } from "@expo/vector-icons";
import * as ScreenOrientation from "expo-screen-orientation";
import WebView from "react-native-webview";
import YouTube from "react-native-youtube";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Replace 'YOUR_API_KEY' with your actual YouTube API key
//const API_KEY = 'AIzaSyA-UJxhvUIcX1B37R1cPGjTjdYGy2ghfZs';
const API_KEY = "AIzaSyCCZKMFnO_IjJhEvLkMizL_kfIo4z1nsrM";
const CACHE_KEY = "youtubeVideosCache";
const CACHE_EXPIRATION_TIME = 30 * 60 * 1000;

const Latest = () => {
  const [videos, setVideos] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [modalVideo, setmodalVideo] = useState(null);
  const windowWidth = Dimensions.get("window").width;
  const [orientation, setOrientation] = useState("");
  const [numColumns, setnumColumns] = useState(2);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const insets = useSafeAreaInsets();
  const top = Platform.OS === "android" ? 0 : insets.top;

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const webViewRef = useRef(null);
  const handleNavigationStateChange = (navState) => {
    if (navState.url.includes("youtube.com/watch")) {
      // Suggestion clicked, handle fullscreen behavior
    }
  };
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      // Check if the swipe is from right to left
      if (gestureState.dx < -50) {
        console.log("Swiped");
        setIsDrawerOpen(true);
        return true;
      }
      return false;
    },
  });

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  // Function to close the drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };
  useEffect(() => {
    // Fetch the latest YouTube videos using the YouTube Data API

    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Check if cached data exists and is still valid
        const cachedData = await AsyncStorage.getItem(CACHE_KEY);
        const cachedTimestamp = await AsyncStorage.getItem(
          `${CACHE_KEY}_timestamp`
        );

        if (cachedData && cachedTimestamp) {
          const currentTime = new Date().getTime();
          const cachedTime = parseInt(cachedTimestamp, 10);

          if (currentTime - cachedTime < CACHE_EXPIRATION_TIME) {
            // Data is still valid, use it
            const cachedVideos = JSON.parse(cachedData);
            setVideos(cachedVideos);
            return;
          }
        }

        // Fetch new data if cache is expired or doesn't exist
        const response = await axios.get(
          `https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet&maxResults=20&playlistId=UULFK8LcweIXy4BGzTqjoj01DA&key=${API_KEY}`
        );

        const data = response.data;

        const videoItems = data.items.map((item) => ({
          id: item.id,
          resource: item.snippet.resourceId.videoId,
          title: item.snippet.title,
          thumbnail: item.snippet.thumbnails.default.url,
        }));

        // Update the cache with the fetched data
        await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(videoItems));
        await AsyncStorage.setItem(
          `${CACHE_KEY}_timestamp`,
          String(new Date().getTime())
        );

        setVideos(videoItems);
      } catch (error) {
        console.error("Error fetching videos:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
    getOrientation();
  }, []);

  const getOrientation = () => {
    if (Platform.OS === "android") {
      if (Dimensions.get("window").height < Dimensions.get("window").width) {
        if (Dimensions.get("window").width >= 1200) {
          setOrientation("LANDSCAPE_ALT");
          setnumColumns(6);
        } else if (Dimensions.get("window").width >= 1100) {
          setOrientation("LANDSCAPE_LOW_ALT");
          setnumColumns(5);
        } else {
          setOrientation("LANDSCAPE");
          setnumColumns(4);
        }
      } else {
        setOrientation("PORTRAIT");
        setnumColumns(2);
      }
      return orientation;
    }
  };

  const handleShouldStartLoadWithRequest = (event) => {
    if (event.url.startsWith("https://www.youtube.com/watch?v=")) {
      if (Platform.OS === "ios") {
        Linking.openURL(event.url);
        return false;
      }
    }
    return true;
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity onPress={() => handleVideoPress(item.resource)}>
      <View style={styles.videoItem}>
        <View style={styles.thumbnailContainer}>
          <Image
            source={{
              uri: item.thumbnails ? item.thumbnails.medium : item.thumbnail,
            }}
            style={styles.thumbnail}
          />
          <TouchableOpacity
            onPress={() => handlePlayButtonPress(item.resource)}
            style={styles.playButton}
          >
            {/* Add your play button icon here */}
            <FontAwesome name="play" size={20} color={"#fff"}></FontAwesome>
          </TouchableOpacity>
          {/*  <View style={styles.durationOverlay}>
          <Text style={styles.durationText}>5:30</Text>
        </View> */}
        </View>
        <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
          {item.title}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const handleVideoPress = (videoId) => {
    // Handle video press (e.g., open the video in a WebView or YouTube app)
    setmodalVideo(videoId);
    setShowModal(true);
    // if (Platform.OS === "android") {
      ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
    // }
  };

  const handlePlayButtonPress = (videoId) => {
    // Handle play button press (e.g., start playing the video)
    setmodalVideo(videoId);
    setShowModal(true);
    if (Platform.OS === "android") {
      ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
    }
  };

  const handleLoadStart = () => {
    setLoading(true);
  };

  const handleLoadEnd = () => {
    setLoading(false);
  };

  // const injectFullScreenScript = async () => {
  //   const script = `
    
  //   (function() {
    
  //   var player = document.getElementById('player-container-id');
    
  //   player.addEventListener('fullscreenchange', function() {
    
  //   if (document.fullscreenElement) {
    
  //   // Fullscreen entered, adjust dimensions (example)
    
  //   player.style.width = '20%';
    
  //   player.style.height = '40%';
    
  //   } else {
    
  //   // Fullscreen exited, reset dimensions (optional)
    
  //   }
    
  //   });
    
  //   })();
    
  //   `;

  //   await webViewRef.current.injectJavaScript(script);
  // };

  // useEffect(() => {
  //   injectFullScreenScript();
  // }, []);



  // const handleShouldStartLoadWithRequest = (event) => {
  //   if (Platform.OS === 'android') {
  //     const fullScreenUrl = event.url.startsWith('https://www.youtube.com/watch?v=');
  //     if (fullScreenUrl) {
  //       return true;
  //     }
  //   }
  //   return true;
  // };

  // const handleNavigationStateChange = (navState) => {
  //   if (Platform.OS === 'ios') {
  //     const fullScreenUrl = navState.url.includes('full') || navState.url.includes('fullscreen');
  //     if (fullScreenUrl) {
  //       webViewRef.current.goBack();
  //     }
  //   }
  // };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: "Latest",
          header: () => (
            <SecondHeader
              title={TEXTDATA.latestTitlebarName}
              isDrawerOpen={isDrawerOpen}
              openDrawer={openDrawer}
              closeDrawer={closeDrawer}
            ></SecondHeader>
          ),
        }}
      />
      <View style={styles.container} {...panResponder.panHandlers}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#fff" />
          </View>
        ) : (
          <FlatList
            key={orientation}
            data={videos}
            renderItem={renderItem}
            keyExtractor={(item) => item.resource}
            numColumns={numColumns}
          />
        )}
      </View>
      <Modal
        animationType="slide"
        visible={showModal}
        statusBarTranslucent={true}
        onRequestClose={() => {
          ScreenOrientation.unlockAsync();
        }}
      >
        <View style={styles.modal}>
          <TouchableOpacity
            style={[
              styles.closeButton,
              { top: Platform.OS === "android" ? 20 : top + 5 },
            ]}
            onPress={() => {
              // ScreenOrientation.unlockAsync();
              setShowModal(false);
            }}
          >
            <FontAwesome name="close" size={30} color="white" />
          </TouchableOpacity>
          <View
            style={{
              height: (Dimensions.get("screen").width * 9) / 16,
              width: (Dimensions.get("screen").width ),
              aspectRatio: 16/9,
            }}
          >
            <WebView
              // ref={webViewRef}
              // contentInset={{top: 200, left: 0, bottom: 200, right: 0}}
              source={{ uri: `https://www.youtube.com/embed/${modalVideo}` }}
              style={styles.webView}
              allowsFullscreenVideo
              onLoadStart={handleLoadStart}
              onLoadEnd={handleLoadEnd}
              statusBarTranslucent={false}
              transparent={false}
              contentMode="mobile"
              javaScriptEnabled={true}
              domStorageEnabled={true}
              // onNavigationStateChange={handleNavigationStateChange}
              // onShouldStartLoadWithRequest={handleShouldStartLoadWithRequest}
            />
          </View>

          {/* <WebView
              style={styles.webView}
              allowsFullscreenVideo
              onLoadStart={handleLoadStart}
              onLoadEnd={handleLoadEnd}
              statusBarTranslucent={false}
              transparent={false}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              source={{ uri: `https://www.youtube.com/embed/${modalVideo}` }}
            /> */}
          {/* 
          <WebView
            style={styles.webView}
            allowsFullscreenVideo
            onLoadStart={handleLoadStart}
            onLoadEnd={handleLoadEnd}
            scalesPageToFit={Platform.OS === "android" ? false : true}
            contentMode="mobile"
            javaScriptEnabled={true}
            domStorageEnabled={true}
            source={{ uri: `https://www.youtube.com/embed/${modalVideo}` }}
            onShouldStartLoadWithRequest={handleShouldStartLoadWithRequest}
          /> */}

          {/* <YouTube
            videoId={modalVideo}
            style={styles.webView}
            controls={1}
            onLoadStart={handleLoadStart}
            onError={(error) => console.error("YouTube Error:", error)}
            onLoadEnd={handleLoadEnd}
            play={true}
            fullscreen
          /> */}
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={COLORS.textColor} />
            </View>
          )}
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  videoItem: {
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    margin: 4, // Add margin to separate items
    borderRadius: 4, // Add rounded corners
    borderWidth: 1, // Add border,
    borderColor: "rgba(0,0,0,0.20)",

    width: Dimensions.get('screen').width/2.2,
  },
  thumbnailContainer: {
    position: "relative", // Create a positioning context
    width: "100%",
    aspectRatio: 16 / 9, // Set a 16:9 aspect ratio for the thumbnail container
  },
  thumbnail: {
    width: "100%",
    height: "100%", // Ensure the thumbnail fills the container
    // Add rounded corners  borderRadius: 4
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  playButton: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 14,
    margin: 2, // Spacing between thumbnail and title
    color: COLORS.textColor,
    textAlign: "left",
    fontFamily: FONT.medium,
  },
  container: {
    flex: 1,
    padding: 5,
    alignItems: "center",
    backgroundColor: COLORS.backgroundColor,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  durationOverlay: {
    position: "absolute", // Position the duration overlay
    bottom: 0, // Place it at the bottom
    left: 0, // Place it at the left
    backgroundColor: "rgba(0, 0, 0, 0.6)", // Customize the overlay color
    paddingHorizontal: 4, // Add horizontal padding
    paddingVertical: 2, // Add vertical padding
    borderBottomRightRadius: 4, // Add rounded corners to the overlay
  },
  durationText: {
    fontSize: 12,
    color: "#fff", // Customize the text color
  },
  modal: {
    flex: 1,
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: 'center',
    backgroundColor: COLORS.backgroundColor,
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  closeButton: {
    position: "absolute",
    left: 20,
    zIndex: 1,
  },
  webView: {
    // height:100,
    //  (Dimensions.get("screen").width * 9) / 16,
    // width:100,
    //  (Dimensions.get("screen").width),
    // height:'100%', 
    // width: '100%',
    // alignItems: "center",
    // aspectRatio: 16 / 9,
    backgroundColor: COLORS.backgroundColor,
    // top: 0,
    // bottom: 0,
    // left: 0,
    // right: 0,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0 0, 0.8)",
  },
});

export default Latest;
