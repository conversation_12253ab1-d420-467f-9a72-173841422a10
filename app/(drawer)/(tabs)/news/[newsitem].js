import React, { useEffect, useState } from "react";
import {
  Text,
  View,
  Image,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
 Platform,
  TouchableOpacity,
  ScrollView
} from "react-native";
import { useLocalSearchParams, Stack } from "expo-router";
import ThirdHeader from "../../../../components/header/thirdHeader";
import { COLORS, FONT } from "../../../../constants";
import { useNews } from "../../../../context/news";
import { FontAwesome } from "@expo/vector-icons";
import { extractContent } from "../../../../api/core";
import * as Linking from "expo-linking";

const NewsDetailsPage = () => {
  const { newsitem } = useLocalSearchParams();
  const { news } = useNews();
  const [loading,setLoading] = useState(true);
  const width = Dimensions.get("window").width;
  const content  = extractContent(news.post_content);

  const handleLinkClick = ()=>
  {
    
  }
  
  useEffect(() => {
   setLoading(false);
   
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <Stack.Screen
        options={{
          headerShown: true,
          title: "News",
          header: () => <ThirdHeader title={newsitem} />,
        }}
      />

      {!loading  ? (
        <View style={{ flex: 1 }}>
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: news.attachment }}
              style={{
                width: width <= 400 ? width : 300,
                backgroundColor: COLORS.gray2,
                aspectRatio: 16 / 9,
              }}
              alt={news.post_title}
            />
          </View>

          {/* Icon with text inline as title */}

          {/* Horizontal flatlist */}
          <View style={styles.detailsContainer}>
            <View style={styles.iconWithTitle}>
              {/* Using Font Awesome calendar icon */}
              <FontAwesome name="calendar" size={24} color={"black"} />
              <Text style={styles.titleIconText}>{news.post_date}</Text>
            </View>

            <ScrollView contentContainerStyle={styles.descriptionContainer}>
              <Text style={styles.titleText}>{news.post_title}</Text>
              {/* Description text */}
              <Text style={styles.titeDescription} >{content}</Text>



            </ScrollView>

          
          </View>
        </View>
      ) : (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={"#000"} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: COLORS.gray3,
    paddingVertical: 5,
  },
  iconWithTitle: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  titleIconText: {
    fontSize: 18,
    paddingHorizontal: 10,
    paddingVertical: 5,
    color: COLORS.textSecondColor,
    fontFamily:FONT.medium,
  },
  titleText: {
    fontSize: 18,
    paddingHorizontal: 10,
    paddingVertical: 5,
    color: COLORS.textSecondColor,
    fontFamily:FONT.medium,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  titleHeader: {
    fontSize: 16,
    paddingHorizontal: 10,
    color: COLORS.textSecondColor,
    paddingVertical: 15,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  titeDescription: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    fontSize: 16,
    color: COLORS.textSecondColor,
    fontFamily:FONT.light,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  titeDescriptionHeader: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    fontSize: 14,
    color: COLORS.textSecondColor,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  titeLink:
  {
    color:"blue",
    paddingVertical: 5,
    paddingHorizontal: 10,
    fontSize: 14,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  detailsContainer: {
    flex: 1,
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  descriptionContainer: {
    padding: 5,
  },
 
});

export default NewsDetailsPage;
