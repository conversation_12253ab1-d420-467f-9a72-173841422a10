import { useState } from "react";
import { Stack } from "expo-router";
import { Text, View,StyleSheet,PanResponder } from "react-native";
import SecondHeader from "../../../../components/header/secondHeader";
import { COLORS, TEXTDATA } from "../../../../constants";
import NewsList from "../../../../components/list/newslist";

export default function News() {

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      // Check if the swipe is from right to left and it's a drag (not a simple swipe)
      if (gestureState.dx < -50 && Math.abs(gestureState.vx) < 0.5) {
        setIsDrawerOpen(true);
        return true;
      }
      return false;
    },
  });

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  // Function to close the drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.backgroundColor,
        height: "100%",
      }}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          title: TEXTDATA.homeBarName,
          header: () => (
            <SecondHeader title={TEXTDATA.newsTitlebarName} isDrawerOpen={isDrawerOpen} openDrawer={openDrawer} closeDrawer={closeDrawer}></SecondHeader>
          ),
        }}
      />
      <View style={styles.centerContent}  {...panResponder.panHandlers}>
       <NewsList></NewsList>
      </View>
    </View>
  );
}
const styles = StyleSheet.create({
 
  centerContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    width:"100%",
  },
});
