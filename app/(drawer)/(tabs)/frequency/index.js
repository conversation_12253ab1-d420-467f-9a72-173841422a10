import { useState } from "react";
import { Stack } from "expo-router";
import { Text, View,StyleSheet,PanResponder } from "react-native";
import SecondHeader from "../../../../components/header/secondHeader";
import { COLORS, TEXTDATA } from "../../../../constants";
import Frequencytab from "../../../../components/custom/frequency";

export default function Frequency() {

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);


  const openDrawer = () => {
    setIsDrawerOpen((prev)=>{return !prev;});
    
  };

  // Function to close the drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };


  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.backgroundColor,
        height: "100%",
      }}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          title: "Latest",
          header: () => (
            <SecondHeader title={TEXTDATA.frequencyTitlebarName} isDrawerOpen={isDrawerOpen} openDrawer={openDrawer} closeDrawer={closeDrawer}></SecondHeader>
          ),
        }}
      />
      
      <Frequencytab ></Frequencytab>
     
    </View>
  );
}
const styles = StyleSheet.create({
 
  centerContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    width:"100%",
    backgroundColor:"#fff"
  },
});
