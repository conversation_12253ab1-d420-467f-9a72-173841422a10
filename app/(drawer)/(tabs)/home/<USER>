import { Modal,View,TouchableOpacity,StyleSheet,Dimensions } from "react-native";
import { useState,useEffect } from "react";
import * as ScreenOrientation from 'expo-screen-orientation';
import { router,useLocalSearchParams,Stack } from "expo-router";
import { COLORS,TEXTDATA } from "../../../../constants";
import { FontAwesome } from "@expo/vector-icons";
import VideoPlayer from "../../../../components/video/video";
import { useHome } from "../../../../context/home";


const FullScreen = ()=>
{
    const { full,pos } = useLocalSearchParams();
    const [showModal, setShowModal] = useState(false);
    const { width, height } = Dimensions.get("window");
    const {home} = useHome();
   
    
  const videoBitrates = {
    "1080p":
      "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/pub-nooldraybinbdh/live_1080p/chunks_dvr.m3u8",
    "720p":
      "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/pub-nooldraybinbdh/live_720p/chunks_dvr.m3u8",
    "480p":
      "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/pub-nooldraybinbdh/live_480p/chunks_dvr.m3u8",
    "360p":
      "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/pub-nooldraybinbdh/live_360p/chunks_dvr.m3u8",
    "auto": "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/playlist_dvr.m3u8",
  };

  const secondBitrates = {
    "1080p":
      "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/pub-noalrayy3pwz0l/live_1080p/chunks_dvr.m3u8",
    "720p":
      "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/pub-noalrayy3pwz0l/live_720p/chunks_dvr.m3u8",
    "480p":
      "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/pub-noalrayy3pwz0l/live_480p/chunks_dvr.m3u8",
    "360p":
      "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/pub-noalrayy3pwz0l/live_360p/chunks_dvr.m3u8",
    "auto": "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/playlist_dvr.m3u8",
  };

  useEffect(()=>{
    ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
     setShowModal(true);
  },[])

    return (<View style={{flex:1,backgroundColor:"#000"}}>
          <Stack.Screen
        options={{
          headerShown:false,
          title: TEXTDATA.homeBarName,
        
        }}
      />

<Modal
        animationType="slide"
        visible={showModal}
        statusBarTranslucent={true}
        onRequestClose={() => {
          ScreenOrientation.unlockAsync();
        }}
       
      >
        
        <View style={styles.modal}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => {
              ScreenOrientation.unlockAsync();
              setShowModal(false);
              router.back();
            }}
          >
            <FontAwesome name="close" size={30} color="white" />
          </TouchableOpacity>
         
          <VideoPlayer
           videoBitRate={full==="alrayyan" ? secondBitrates : videoBitrates}
           full={true}
           width={width}
           height={(9 / 16) * width}
           actualHeight={height}
           quality={"auto"}
           channel={full==="alrayyan" ? true : false}
           position={home?home.channel===full?home.postion:pos:pos}
          >

          </VideoPlayer>
    
         
        </View>
      </Modal>
    </View>)
}

const styles = StyleSheet.create({
    playButton: {
      position: "absolute",
      alignItems:"center",
      justifyContent:"center",
      
      backgroundColor: "rgba(0,0,0,0.7)",
      width: 46,
      height: 46,
      borderRadius: 50,
      alignItems: "center",
      justifyContent: "center",
    },
    modal: {
      flex: 1,
      width:"100%",
      height:"100%",
      alignItems:"center",
      backgroundColor: COLORS.backgroundColor,
      top:0,
      bottom:0,
      left:0,
      right:0
     
      
    },
     closeButton: {
      position: 'absolute',
      top: 20,
      left: 20,
      zIndex: 1,
    }
  });

export default FullScreen;