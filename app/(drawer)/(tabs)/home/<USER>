import React, { useState, useEffect, useRef } from "react";
import {
  View,
  SafeAreaView,
  ScrollView,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  PanResponder,
  Modal
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Stack } from "expo-router";
import CustomHeader from "../../../../components/header/customHeader";
import homeStyles from "../../../../styles/pages/home.style";
import { COLORS, NUMBERS, TENS, TEXTDATA } from "../../../../constants";

// import { Video } from 'expo-av';
import SocialMediaList from "../../../../components/footer/social";
import VideoPlayer from "../../../../components/video/video";
import { MaterialCommunityIcons } from "@expo/vector-icons";


const Home = () => {

  const mounted = useRef(true);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { width, height } = Dimensions.get("window");
  const [isPlaying, setIsPlaying] = useState(true); // Start with autoplay enabled
  const [activeButton, setActiveButton] = useState(true);
  

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      const leftEdgeWidth = 50; // Adjust this value as needed
      const swipeThreshold = -50; // Adjust this value as needed
  
      // Check if the swipe starts from the left edge and is right-to-left
      if (
        gestureState.x0 < leftEdgeWidth &&
        gestureState.dx < swipeThreshold &&
        Math.abs(gestureState.vx) < 0.5
      ) {
        setIsDrawerOpen(true);
        return true;
      }
  
      return false;
    },
  });
  const activeBtnColor = [
    COLORS.homeTertaryBtnColor,
    COLORS.homeSecondBtnColor,
    COLORS.homeTertaryBtnColor,
    COLORS.primaryHlColor,
    COLORS.homePrimaryBtnColor,
  ];
  const disableBtnColor = [
    COLORS.homedisabledFourthBtnColor,
    COLORS.homedisabledSecondBtnColor,
    COLORS.homedisabledTertaryBtnColor,
    COLORS.homedisableColor,
    COLORS.homedisabledPrimaryBtnColor,
  ];

  const videoBitrates = {
    "1080p":
      "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/pub-nooldraybinbdh/live_1080p/chunks_dvr.m3u8",
    "720p":
      "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/pub-nooldraybinbdh/live_720p/chunks_dvr.m3u8",
    "480p":
      "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/pub-nooldraybinbdh/live_480p/chunks_dvr.m3u8",
    "360p":
      "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/pub-nooldraybinbdh/live_360p/chunks_dvr.m3u8",
    "auto": "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/playlist_dvr.m3u8",
  };

  const secondBitrates = {
    "1080p":
      "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/pub-noalrayy3pwz0l/live_1080p/chunks_dvr.m3u8",
    "720p":
      "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/pub-noalrayy3pwz0l/live_720p/chunks_dvr.m3u8",
    "480p":
      "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/pub-noalrayy3pwz0l/live_480p/chunks_dvr.m3u8",
    "360p":
      "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/pub-noalrayy3pwz0l/live_360p/chunks_dvr.m3u8",
    "auto": "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/playlist_dvr.m3u8",
  };

  //On Press Function
  const handlePress = () => {
    setActiveButton(!activeButton);
  };

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  // Function to close the drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  // Call the pauseVideo function when you want to pause the video
  const pauseVideo = async () => {
    if (videoRef.current) {
      await videoRef.current.pauseAsync();
      // Update any state variables related to video playback here if needed
    }
  };

  // loadVideo

  const loadVideo = async () => {
    if (videoRef.current && mounted.current) {
      try {
        await videoRef.current.loadAsync({
          uri: "https://alrayyancdn.vidgyor.com/pub-noalrayy3pwz0l/liveabr/playlist_dvr.m3u8",
        });
        // Start playing the video when it's loaded
        videoRef.current.playAsync();
      } catch (error) {
        console.error("Error loading video:", error);
      }
    }
  };
  // unloadVideo
  const unloadVideo = async () => {
    if (!mounted.current && videoRef.current) {
      try {
        await videoRef.current.unloadAsync();
      } catch (error) {
        console.error("Error unloading video:", error);
      }
    }
  };

  useEffect(() => {
  
  }, []);



  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: TEXTDATA.homeBarName,
          header: () => (
            <CustomHeader title={TEXTDATA.homeBarName} isDrawerOpen={isDrawerOpen} openDrawer={openDrawer} closeDrawer={closeDrawer} ></CustomHeader>
          ),
        }}
      />

   
      <View style={homeStyles.container} >
        <View style={homeStyles.buttonContainer}>
          <TouchableOpacity style={homeStyles.button} onPress={handlePress}>
            <LinearGradient
              colors={activeButton ? activeBtnColor : disableBtnColor}
              locations={[0.1, 0.5, 0.5, 0.75, 1]}
              start={{ x: NUMBERS.zero, y: NUMBERS.zero }}
              end={{ x: NUMBERS.zero, y: NUMBERS.one }}
              style={{ ...StyleSheet.absoluteFillObject }}
            />
            <View style={homeStyles.overlay}></View>
            <View style={homeStyles.buttonContent}>
              <MaterialCommunityIcons
                name={"television-classic"}
                size={TENS.twenty}
                color={COLORS.textColor}
                style={homeStyles.buttonIcon}
              ></MaterialCommunityIcons>
              <Text style={homeStyles.buttonText}>
                {TEXTDATA.homeBtnFirstTxt}
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={homeStyles.button} onPress={handlePress}>
            <LinearGradient
              colors={!activeButton ? activeBtnColor : disableBtnColor}
              locations={[0.1, 0.5, 0.5, 0.75, 1]}
              start={{ x: NUMBERS.zero, y: NUMBERS.zero }}
              end={{ x: NUMBERS.zero, y: NUMBERS.one }}
              style={{ ...StyleSheet.absoluteFillObject }}
            />
            <View style={homeStyles.overlay}></View>
            <View style={homeStyles.buttonContent}>
              <MaterialCommunityIcons
                name={"television-classic"}
                size={TENS.twenty}
                color={COLORS.textColor}
                style={homeStyles.buttonIcon}
              ></MaterialCommunityIcons>
              <Text style={homeStyles.buttonText}>
                {TEXTDATA.homeBtnSecondTxt}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        <SafeAreaView style={homeStyles.safeArea}>
          <ScrollView
            contentContainerStyle={{
              flex: 1,
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <View style={homeStyles.videoContainer}>
              {/*  <Video
        ref={videoRef}
        style={{ width:width, height: (9 / 16) * width ,aspectRatio:16/9 }}
        source={{
          uri: 'https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/liveabr/playlist_dvr.m3u8',
        }}
        resizeMode="contain"
        onPlaybackStatusUpdate={isPlaying => setIsPlaying(() => isPlaying)}
        useNativeControls
        shouldPlay
    
      /> */}

              <VideoPlayer
                videoBitRate={activeButton ? secondBitrates : videoBitrates}
                width={width}
                height={(9 / 16) * width}
                actualHeight={height}
                quality={"auto"}
                channel={activeButton}
                full={false}
              ></VideoPlayer>
            </View>
          </ScrollView>
        </SafeAreaView>
        <SocialMediaList></SocialMediaList>
      </View>
   
    </>
  );
};

export default Home;
