import { useState } from "react";
import { Stack } from "expo-router";
import { Text, View,StyleSheet,ActivityIndicator,PanResponder  } from "react-native";
import SecondHeader from "../../../../components/header/secondHeader";
import {TEXTDATA,COLORS } from "../../../../constants";
import ContestList from "../../../../components/list/contestList";

export default function Contest() {

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      // Check if the swipe is from right to left
      if (gestureState.dx < -50) {
        console.log("Swiped");
        setIsDrawerOpen(true);
        return true;
      }
      return false;
    },
  });

    
  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  // Function to close the drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: "مسابقات",
          header: () => (
            <SecondHeader title={TEXTDATA.contestTitlebarName} isDrawerOpen={isDrawerOpen} openDrawer={openDrawer} closeDrawer={closeDrawer}></SecondHeader>
          ),
        }}
      />
        <View style={{flex:1,backgroundColor:COLORS.backgroundColor}} {...panResponder.panHandlers}>
        <ContestList></ContestList>
    </View>
    </>
  );
}
const styles = StyleSheet.create({
 
  centerContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    width:"100%",
  },
  container:
  {
    flex:1,
    padding:5,
    alignItems:'center',
    backgroundColor:COLORS.backgroundColor,
    
  }
});
