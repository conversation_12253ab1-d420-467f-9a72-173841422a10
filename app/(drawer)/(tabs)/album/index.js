import { useState } from "react";

import { Stack } from "expo-router";
import { Text, View,StyleSheet,PanResponder } from "react-native";
import SecondHeader from "../../../../components/header/secondHeader";
import { COLORS, TEXTDATA } from "../../../../constants";
import AlbumList from "../../../../components/list/albumList";


//Page for Displaying the Album Page.

export default function Album() {

  // State to track whether the drawer is open or not
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      // Check if the swipe is from right to left
      if (gestureState.dx < -50) {
        console.log("Swiped");
        setIsDrawerOpen(true);
        return true;
      }
      return false;
    },
  });

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  // Function to close the drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };



  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.backgroundColor,
        height: "100%",
      }}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          title: "Albums",
          header: () => (
            //Custom Header
            <SecondHeader title={TEXTDATA.albumTitlebarName} isDrawerOpen={isDrawerOpen} openDrawer={openDrawer} closeDrawer={closeDrawer}></SecondHeader>
          ),
        }}
      />
      <View style={styles.centerContent} {...panResponder.panHandlers}>
        <AlbumList></AlbumList>
      </View>
    </View>
  );
}

//Styles for Album Page
const styles = StyleSheet.create({
 
  centerContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    width:"100%",
  },
});
