import { useState } from "react";
import { Stack,useLocalSearchParams } from "expo-router";
import {useAlbum} from "../../../../context/album";
import { Text, View,StyleSheet } from "react-native";
import { COLORS, TEXTDATA } from "../../../../constants";
import ThirdHeader from "../../../../components/header/thirdHeader";
import ImageList from "../../../../components/list/imageList";
import SliderModal from "../../../../components/custom/slider";


export default function Gallery() {

    const { gallery } = useLocalSearchParams();
    const [index,setIndex] = useState(0);
    const [isModalVisible, setModalVisible] = useState(false);
    const {image} = useAlbum();

    const toggleModal = (id) => {
        const pos = image.findIndex((item) => item.id === id);
        setIndex(pos);
        setModalVisible(!isModalVisible);
      };
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.backgroundColor,
        height: "100%",
      }}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          title:gallery,
          header: () => (
           <ThirdHeader title={gallery}></ThirdHeader>
          ),
        }}
      />
      <View style={styles.centerContent}>
       <ImageList images={image} toggleModal={toggleModal}></ImageList>
      </View>
      <SliderModal
        isVisible={isModalVisible}
        images={image}
        index={index}
        onClose={toggleModal}
      />
    </View>
  );
}
const styles = StyleSheet.create({
 
  centerContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    width:"100%",
  },
});
