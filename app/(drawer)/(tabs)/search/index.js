import React, { useState, useEffect } from "react";
import { Stack } from "expo-router";
import SecondHeader from "../../../../components/header/secondHeader";
import { COLORS, FONT } from "../../../../constants";
import * as ScreenOrientation from "expo-screen-orientation";
import * as Linking from "expo-linking";
import { FontAwesome } from "@expo/vector-icons";
import {
  View,
  TextInput,
  FlatList,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  PanResponder,
  Dimensions,
  StyleSheet,
  Image,
} from "react-native";
import axios from "axios";

const API_KEY = "AIzaSyCCZKMFnO_IjJhEvLkMizL_kfIo4z1nsrM";
const CHANNEL_ID = "UCLFK8LcweIXy4BGzTqjoj01DA";
const CHANNEL_NAME = "AlrayyanTV";

const SearchPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [videos, setVideos] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const windowWidth = Dimensions.get("window").width;
  const [orientation, setOrientation] = useState("");
  const [numColumns, setnumColumns] = useState(2);

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      // Check if the swipe is from right to left
      if (gestureState.dx < -50) {
        console.log("Swiped");
        setIsDrawerOpen(true);
        return true;
      }
      return false;
    },
  });

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  // Function to close the drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  const fetchVideos = async (query) => {
    setIsLoading(true);
    try {
      if (query) {
        const response = await axios.get(
          `https://youtube.googleapis.com/youtube/v3/search?part=snippet&channelId=UCK8LcweIXy4BGzTqjoj01DA&q=${query}&key=${API_KEY}`
        );
        const data = response.data;
        console.log(data)
        const videoItems = data.items.map((item) => ({
          id: item.id,
          resource: item.snippet.resourceId.videoId,
          title: item.snippet.title,
          thumbnail: item.snippet.thumbnails.default.url,
        }));
        setVideos(videoItems);
      } else {
        const response = await axios.get(
          `https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet&maxResults=20&playlistId=UULFK8LcweIXy4BGzTqjoj01DA&key=${API_KEY}`
        );
        const data = response.data;
        console.log(data.items[0])
        const videoItems = data.items.map((item) => ({
          id: item.id,
          resource: item.snippet.resourceId.videoId,
          title: item.snippet.title,
          thumbnail: item.snippet.thumbnails.default.url,
        }));
        setVideos(videoItems);
      }
    } catch (error) {
      console.error("Error fetching videos:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchVideos("");
    getOrientation();
  }, []);

  const getOrientation = () => {
    if (Dimensions.get("window").height < Dimensions.get("window").width) {
      if (Dimensions.get("window").width >= 1200) {
        setOrientation("LANDSCAPE_ALT");
        setnumColumns(6);
      } else if (Dimensions.get("window").width >= 1100) {
        setOrientation("LANDSCAPE_LOW_ALT");
        setnumColumns(5);
      } else {
        setOrientation("LANDSCAPE");
        setnumColumns(4);
      }
    } else {
      setOrientation("PORTRAIT");
      setnumColumns(2);
    }
    return orientation;
  };
  const renderItem = ({ item }) => (
    <TouchableOpacity   onPress={() =>  handleVideoPress(item.resource)}>
      <View style={styles.videoItem}>
        <View style={styles.thumbnailContainer}>
          <Image
            source={{
              uri: item.thumbnails ? item.thumbnails.medium : item.thumbnail,
            }}
            style={styles.thumbnail}
          />
          <TouchableOpacity
            onPress={() =>  handleVideoPress(item.resource)}
            style={styles.playButton}
          >
            {/* Add your play button icon here */}
            <FontAwesome name="play" size={20} color={"#fff"}></FontAwesome>
          </TouchableOpacity>
          {/*  <View style={styles.durationOverlay}>
          <Text style={styles.durationText}>5:30</Text>
        </View> */}
        </View>
        <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
          {item.title}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const handleVideoPress = (video) => {
    // Implement navigation to the video detail page or other actions 
    Linking.openURL("https://www.youtube.com/watch?v="+video)
  };

  return (
    <>
     <Stack.Screen
        options={{
          headerShown: true,
          title: "يبحث",
          header: () => (
            <SecondHeader
              title={"يبحث"}
              isDrawerOpen={isDrawerOpen}
              openDrawer={openDrawer}
              closeDrawer={closeDrawer}
            ></SecondHeader>
          ),
        }}
      />
   
    <View style={styles.container} {...panResponder.panHandlers}>
     
      <TextInput
        placeholder="يبحث"
        onChangeText={setSearchQuery}
        value={searchQuery}
        onSubmitEditing={() => fetchVideos(searchQuery)}
        placeholderTextColor={"#000"}
        style={{
          borderWidth: 1,
          borderColor: "gray", // Customize the border color
          borderRadius: 10, // Customize the border radius
          padding: 10, // Add padding to the input
          textAlign: "right", // Align text to the right
          backgroundColor:COLORS.textColor,
          color:"#000",
          width:"100%",
        
        }}
      />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      ) : (
        <FlatList
          key={orientation}
          data={videos}
          renderItem={renderItem}
          keyExtractor={(item) => item.resource}
          numColumns={numColumns}
          contentContainerStyle={{paddingVertical:5}}
        />
      )}
    </View>
    </>
  );
};
const styles = StyleSheet.create({
  videoItem: {
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    margin: 4, // Add margin to separate items
    borderRadius: 4, // Add rounded corners
    borderWidth: 1, // Add border,
    borderColor: "rgba(0,0,0,0.20)",

    width: 185,
  },
  thumbnailContainer: {
    position: "relative", // Create a positioning context
    width: "100%",
    aspectRatio: 16 / 9, // Set a 16:9 aspect ratio for the thumbnail container
  },
  thumbnail: {
    width: "100%",
    height: "100%", // Ensure the thumbnail fills the container
    // Add rounded corners  borderRadius: 4
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  playButton: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 14,
    margin: 2, // Spacing between thumbnail and title
    color: COLORS.textColor,
    textAlign: "left",
    fontFamily: FONT.medium,
  },
  container: {
    flex: 1,
    padding: 5,
    alignItems: "center",
    backgroundColor: COLORS.backgroundColor,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  durationOverlay: {
    position: "absolute", // Position the duration overlay
    bottom: 0, // Place it at the bottom
    left: 0, // Place it at the left
    backgroundColor: "rgba(0, 0, 0, 0.6)", // Customize the overlay color
    paddingHorizontal: 4, // Add horizontal padding
    paddingVertical: 2, // Add vertical padding
    borderBottomRightRadius: 4, // Add rounded corners to the overlay
  },
  durationText: {
    fontSize: 12,
    color: "#fff", // Customize the text color
  },
  modal: {
    flex: 1,
    width: "100%",
    height: "100%",
    alignItems: "center",
    backgroundColor: COLORS.backgroundColor,
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  closeButton: {
    position: "absolute",
    top: 20,
    left: 20,
    zIndex: 1,
  },
  webView: {
    flex: 1,
    width: "100%",
    height: "100%",
    aspectRatio: 16 / 9,
    backgroundColor: COLORS.backgroundColor,
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0 0, 0.8)",
  },
});
export default SearchPage;
