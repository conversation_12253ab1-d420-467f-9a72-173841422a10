import React, { useEffect, useState } from "react";
import {
  Text,
  View,
  ScrollView,
  Image,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
  Platform
} from "react-native";
import { useLocalSearchParams, Stack } from "expo-router";
import ThirdHeader from "../../../../components/header/thirdHeader";
import { TEXTDATA, COLORS, FONT } from "../../../../constants";
import { useShow } from "../../../../context/shows";
import { FontAwesome } from "@expo/vector-icons";
import { YT_API_KEY_I } from "../../../../api/common";
import axios from "axios";
import * as Linking from "expo-linking";

const ShowDetailsPage = () => {
  const { program } = useLocalSearchParams();
  const { show } = useShow();
  const [playlistThumbnails, setPlaylistThumbnails] = useState([]);
  const [loading, setLoading] = useState(true);
  const width = Dimensions.get("window").width;

  

  useEffect(() => {
    const fetchPlaylistThumbnails = async () => {
      const playlistData = (show.season_playlist || []).filter(
        (item) => item.link_playlist
      );

      const thumbnailPromises = playlistData.map(
        async (playlistItem, index) => {
          try {
            if (playlistItem.link_playlist) {
              const playlistId = playlistItem.link_playlist.split("list=")[1];
              if (playlistId) {
                const apiKey = YT_API_KEY_I; // Replace with your YouTube Data API key
                const response = await axios.get(
                  `https://www.googleapis.com/youtube/v3/playlists?part=snippet&id=${playlistId}&key=${apiKey}`
                );
                const playlistItemData = response.data.items[0];
                if (playlistItemData) {
                  const thumbnailUrl =
                    playlistItemData.snippet.thumbnails.medium.url;
                  const id = index;

                  return { ...playlistItem, thumbnailUrl, id };
                }
              }

              return playlistItem;
            }
          } catch (error) {
            console.error("Error fetching playlist thumbnail:", error);
            setLoading(false);
            return playlistItem;
          }
        }
      );

      const updatedPlaylistData = await Promise.all(thumbnailPromises);
      setLoading(false);

      setPlaylistThumbnails(updatedPlaylistData.reverse());
    };

    fetchPlaylistThumbnails();
  }, [show.season_playlist]);

  return (
    <View style={{ flex: 1 }}>
      <Stack.Screen
        options={{
          headerShown: true,
          title: "Latest",
          header: () => <ThirdHeader title={program} />,
        }}
      />

      {!loading ? (
        <View style={{ flex: 1 }}>
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: show.logo_url }}
              style={{
                width: width <= 400 ? width : 300,
                backgroundColor: COLORS.gray2,
                aspectRatio: 16 / 9,
              }}
              alt={show.title}
            />
          </View>

          {/* Icon with text inline as title */}

          {/* Horizontal flatlist */}
          <View style={styles.detailsContainer}>
            <View style={styles.iconWithTitle}>
              {/* Using Font Awesome calendar icon */}
              <FontAwesome name="calendar" size={24} color={"black"} />
              <Text style={styles.titleIconText}>{show.day + " " + show.time}</Text>
            </View>

            <ScrollView contentContainerStyle={styles.descriptionContainer}>
              <Text style={styles.titleText}>{show.title}</Text>
              {/* Description text */}
              <Text style={styles.titeDescription}>{show.description}</Text>
            </ScrollView>

            {playlistThumbnails.length > 0 ? (
              <View style={styles.flatListContainer}>
                <Text style={styles.flatListHeading}>{"حلقات البرنامج"}</Text>
                <FlatList
                  data={playlistThumbnails}
                  keyExtractor={(item) => item.id}
                  horizontal
                  // inverted
                  showsHorizontalScrollIndicator={false}
                  renderItem={({ item }) => (
                    <TouchableOpacity onPress={()=>{Linking.openURL(item.link_playlist)}}>
                      <View style={styles.flatListItem} key={item.id}>
                        {item.thumbnailUrl && (
                          <Image
                            source={{ uri: item.thumbnailUrl }}
                            style={styles.flatImage} // Customize image dimensions as needed
                          />
                        )}
                        <View style={styles.overlay} />
                        {/* Display title in the center */}
                        <Text style={styles.titleOverlay} numberOfLines={2}>
                          {item.title_playlist}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  )}
                />
              </View>
            ) : (
              ""
            )}
          </View>
        </View>
      ) : (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={"#000"} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: COLORS.gray3,
    paddingVertical: 5,
  },
  iconWithTitle: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  titleText: {
    fontSize: 18,
    paddingHorizontal: 10,
    paddingVertical: 5,
    color: COLORS.textSecondColor,
    fontFamily:FONT.medium,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  titleIconText:
  {
    fontSize: 18,
    paddingHorizontal: 10,
    paddingVertical: 5,
    color: COLORS.textSecondColor,
    fontFamily:FONT.medium
  },
  titleHeader: {
    fontSize: 16,
    paddingHorizontal: 10,
    color: COLORS.textSecondColor,
    paddingVertical: 15,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  titeDescription: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    fontSize: 16,
    color: COLORS.textSecondColor,
    fontFamily:FONT.light,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  detailsContainer: {
    flex: 1,
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  descriptionContainer: {
    padding: 5,
  },
  flatListHeading: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 12,
    color: COLORS.primaryHlColor,
    paddingVertical: 10,
    paddingHorizontal: 10,
    textAlign:"left",
    direction:"rtl",
    writingDirection:"rtl"
  },
  flatListContainer: {
    paddingHorizontal: 10,
  },
  flatListItem: {
    margin: 2,
  },
  flatImage: {
    width: 165,
    height: 110,
    borderRadius: 25,
    borderWidth: 1,
  },
  overlay: {
    position: "absolute",
    backgroundColor: "rgba(0, 0, 0, 0.2)", // White with some transparency
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
  },
  titleOverlay: {
    color: COLORS.textColor,
    fontSize: 14,
    fontWeight: "bold",
    paddingHorizontal: 10,
    paddingVertical: 5,
    position: "absolute",
    // Center text both horizontally and vertically
    textAlign: "center",
    top: "30%",
  },
  flatListColumn: {},
});

export default ShowDetailsPage;
