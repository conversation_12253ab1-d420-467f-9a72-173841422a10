import { useState } from "react";
import { Stack } from "expo-router";
import { Text, View,StyleSheet,PanResponder, ScrollView } from "react-native";
import SecondHeader from "../../../../components/header/secondHeader";
import { COLORS, TEXTDATA } from "../../../../constants";
import ContactForm from "../../../../components/forms/contactForm";

export default function Contact() {

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      // Check if the swipe is from right to left
      if (gestureState.dx < -50) {
        console.log("Swiped");
        setIsDrawerOpen(true);
        return true;
      }
      return false;
    },
  });

  
  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  // Function to close the drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  return (
    <ScrollView
      style={{
        flex: 1,
        backgroundColor: '#fff',
        height: "100%",
      }}
    >
      <Stack.Screen
        options={{
          headerShown: true,
          title: "Contact",
          header: () => (
            <SecondHeader title={TEXTDATA.contactTitlebarName} isDrawerOpen={isDrawerOpen} openDrawer={openDrawer} closeDrawer={closeDrawer}></SecondHeader>
          ),
        }}
      />
      <View style={styles.centerContent}  {...panResponder.panHandlers}>
       <ContactForm></ContactForm>
      </View>
    </ScrollView>
  );
}
const styles = StyleSheet.create({
 
  centerContent: {
    flex: 1,
    width:"100%",
  },
});
