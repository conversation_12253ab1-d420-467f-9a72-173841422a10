diff --git a/node_modules/react-native-orientation-locker/android/build.gradle b/node_modules/react-native-orientation-locker/android/build.gradle
index a1b2c3d..e4f5g6h 100644
--- a/node_modules/react-native-orientation-locker/android/build.gradle
+++ b/node_modules/react-native-orientation-locker/android/build.gradle
@@ -1,6 +1,7 @@
 apply plugin: 'com.android.library'
 
 android {
+    namespace 'com.github.yamill.orientation'
     compileSdkVersion safeExtGet('compileSdkVersion', 28)
     buildToolsVersion safeExtGet('buildToolsVersion', '28.0.3')