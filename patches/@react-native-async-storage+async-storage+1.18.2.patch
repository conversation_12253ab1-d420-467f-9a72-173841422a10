diff --git a/node_modules/@react-native-async-storage/async-storage/android/build.gradle b/node_modules/@react-native-async-storage/async-storage/android/build.gradle
index a1b2c3d..e4f5g6h 100644
--- a/node_modules/@react-native-async-storage/async-storage/android/build.gradle
+++ b/node_modules/@react-native-async-storage/async-storage/android/build.gradle
@@ -74,6 +74,7 @@ if (useNextStorage) {
 }
 
 android {
+    namespace 'com.reactnativecommunity.asyncstorage'
     compileSdkVersion safeExtGet('compileSdkVersion', 32)
     defaultConfig {
         minSdkVersion safeExtGet('minSdkVersion', 23)
